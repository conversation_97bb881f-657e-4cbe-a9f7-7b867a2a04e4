'use client';

import React, { createContext, useContext, useState, useCallback, ReactNode } from 'react';

interface SmsContextType {
  // Balance management
  balance: number | null;
  setBalance: (balance: number) => void;
  refreshBalance: () => Promise<void>;
  
  // SMS logs refresh
  refreshLogs: () => void;
  logsRefreshTrigger: number;
  
  // General refresh for components
  refreshData: () => void;
  dataRefreshTrigger: number;
}

const SmsContext = createContext<SmsContextType | undefined>(undefined);

interface SmsProviderProps {
  children: ReactNode;
  initialBalance?: number | null;
}

export function SmsProvider({ children, initialBalance = null }: SmsProviderProps) {
  const [balance, setBalance] = useState<number | null>(initialBalance);
  const [logsRefreshTrigger, setLogsRefreshTrigger] = useState(0);
  const [dataRefreshTrigger, setDataRefreshTrigger] = useState(0);

  const refreshBalance = useCallback(async () => {
    try {
      console.log('🔄 Refreshing SMS balance...');
      // Import the action dynamically to avoid circular dependencies
      const { getSmsBalance } = await import('@/lib/actions/sms');
      const result = await getSmsBalance();
      console.log('📊 SMS balance result:', result);

      if (result && typeof result.balance === 'number') {
        console.log('✅ Setting balance to:', result.balance);
        setBalance(result.balance);
      } else {
        console.warn('⚠️ Invalid balance result:', result);
        setBalance(0); // Fallback to 0 if no valid balance
      }
    } catch (error) {
      console.error('❌ Failed to refresh SMS balance:', error);
      setBalance(0); // Fallback to 0 on error
    }
  }, []);

  const refreshLogs = useCallback(() => {
    setLogsRefreshTrigger(prev => prev + 1);
  }, []);

  const refreshData = useCallback(() => {
    setDataRefreshTrigger(prev => prev + 1);
    refreshBalance();
    refreshLogs();
  }, [refreshBalance, refreshLogs]);

  const value: SmsContextType = {
    balance,
    setBalance,
    refreshBalance,
    refreshLogs,
    logsRefreshTrigger,
    refreshData,
    dataRefreshTrigger,
  };

  return (
    <SmsContext.Provider value={value}>
      {children}
    </SmsContext.Provider>
  );
}

export function useSms() {
  const context = useContext(SmsContext);
  if (context === undefined) {
    throw new Error('useSms must be used within a SmsProvider');
  }
  return context;
}

// Hook for components that need to trigger refresh after SMS operations
export function useSmsRefresh() {
  const { refreshData, refreshBalance, refreshLogs } = useSms();

  return {
    // Call this after successful SMS sending
    onSmsSuccess: useCallback(() => {
      refreshData();
    }, [refreshData]),

    // Call this to refresh only balance
    refreshBalance,

    // Call this to refresh only logs
    refreshLogs,
  };
}

// Safe version of SMS refresh hook that doesn't throw error when used outside SMS context
export function useSmsRefreshSafe() {
  const context = useContext(SmsContext);

  if (context === undefined) {
    // Return a no-op function when SMS context is not available
    return {
      onSmsSuccess: () => {}
    };
  }

  const { refreshData, refreshBalance, refreshLogs } = context;

  return {
    // Call this after successful SMS sending
    onSmsSuccess: useCallback(() => {
      refreshData();
    }, [refreshData]),

    // Call this to refresh only balance
    refreshBalance,

    // Call this to refresh only logs
    refreshLogs,
  };
}
