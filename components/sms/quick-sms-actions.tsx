'use client';

import { useEffect, useState, useMemo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { MessageSquare, AlertTriangle, Loader2 } from 'lucide-react';
import { useSafeTranslation } from '@/hooks/use-safe-translation';
import { getPaymentsForSms, getAllActiveAthletesForSms } from '@/lib/actions/sms';
import { Payment } from '@/lib/types';
import ColorfulPaymentReminderDialog from './colorful-payment-reminder-dialog';
import ColorfulTemplateSmsDialog from './colorful-template-sms-dialog';

export default function QuickSmsActions() {
  const { t } = useSafeTranslation();
  const [payments, setPayments] = useState<Payment[]>([]);
  const [allActiveAthletes, setAllActiveAthletes] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Memoize users array to prevent infinite re-renders
  const templateUsers = useMemo(() =>
    allActiveAthletes.map(athlete => ({
      id: athlete.id,
      name: athlete.name || '',
      surname: athlete.surname || '',
      parentPhone: athlete.parentPhone,
      teamName: '', // Athletes don't have direct team names in this context
      status: athlete.status || 'active'
    })), [allActiveAthletes]
  );

  useEffect(() => {
    async function loadData() {
      try {
        const [paymentsResult, athletesResult] = await Promise.all([
          getPaymentsForSms(),
          getAllActiveAthletesForSms()
        ]);

        setPayments((paymentsResult.data || []) as unknown as Payment[]);
        setAllActiveAthletes(athletesResult.data || []);
      } catch (err) {
        console.error('Error loading SMS data:', err);
        setError('Error loading SMS data');
      } finally {
        setLoading(false);
      }
    }

    void loadData();
  }, []);

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <MessageSquare className="h-5 w-5" />
            <span>{t('sms:quickActions.title')}</span>
          </CardTitle>
          <CardDescription>
            {t('sms:quickActions.description')}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center p-8">
            <Loader2 className="h-6 w-6 animate-spin" />
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <MessageSquare className="h-5 w-5" />
            <span>{t('sms:quickActions.title')}</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  // Filter payments that can receive SMS reminders (including partially paid)
  const eligiblePayments = payments.filter(payment =>
    (payment.status === 'pending' || payment.status === 'overdue' || payment.status === 'partially_paid') &&
    payment.athlete?.parentPhone
  );

  // For pending: include pending and partially paid pending payments
  const pendingPayments = eligiblePayments.filter(payment =>
    payment.status === 'pending' ||
    (payment.status === 'partially_paid' && new Date(payment.dueDate) >= new Date())
  );

  // For overdue: include overdue and partially paid overdue payments
  const overduePayments = eligiblePayments.filter(payment =>
    payment.status === 'overdue' ||
    (payment.status === 'partially_paid' && new Date(payment.dueDate) < new Date())
  );

  // For total eligible: use all active athletes with phone numbers (not just those with payments)
  const totalEligibleUsers = allActiveAthletes.filter(athlete => athlete.parentPhone);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <MessageSquare className="h-5 w-5" />
          <span>{t('sms:quickActions.title')}</span>
        </CardTitle>
        <CardDescription>
          {t('sms:quickActions.description')}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {/* Statistics as Clickable Buttons */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Pending Payments Button */}
            {pendingPayments.length > 0 ? (
              <ColorfulPaymentReminderDialog
                payments={pendingPayments}
                defaultTemplateType="pending"
                trigger={
                  <Button
                    variant="outline"
                    className="h-auto p-6 flex flex-col items-center space-y-2 bg-gradient-to-br from-blue-50 to-blue-100 hover:from-blue-100 hover:to-blue-200 border-blue-200 hover:border-blue-300 transition-all duration-300 shadow-sm hover:shadow-md"
                  >
                    <div className="text-3xl font-bold text-blue-600">{pendingPayments.length}</div>
                    <div className="text-sm font-medium text-blue-700">{t('sms:sending.bulk.pending')}</div>
                    <div className="text-xs text-blue-600 opacity-75">✨ {t('sms:colorful.clickToSendReminders')}</div>
                  </Button>
                }
              />
            ) : (
              <div className="h-auto p-6 flex flex-col items-center space-y-2 bg-blue-50 rounded-lg border border-blue-200 opacity-50">
                <div className="text-3xl font-bold text-blue-600">0</div>
                <div className="text-sm font-medium text-blue-700">{t('sms:sending.bulk.pending')}</div>
                <div className="text-xs text-blue-600 opacity-75">{t('sms:colorful.noPendingPayments')}</div>
              </div>
            )}

            {/* Overdue Payments Button */}
            {overduePayments.length > 0 ? (
              <ColorfulPaymentReminderDialog
                payments={overduePayments}
                defaultTemplateType="overdue"
                trigger={
                  <Button
                    variant="outline"
                    className="h-auto p-6 flex flex-col items-center space-y-2 bg-gradient-to-br from-red-50 to-red-100 hover:from-red-100 hover:to-red-200 border-red-200 hover:border-red-300 transition-all duration-300 shadow-sm hover:shadow-md"
                  >
                    <div className="text-3xl font-bold text-red-600">{overduePayments.length}</div>
                    <div className="text-sm font-medium text-red-700">{t('sms:sending.bulk.overdue')}</div>
                    <div className="text-xs text-red-600 opacity-75">🚨 {t('sms:colorful.clickToSendReminders')}</div>
                  </Button>
                }
              />
            ) : (
              <div className="h-auto p-6 flex flex-col items-center space-y-2 bg-red-50 rounded-lg border border-red-200 opacity-50">
                <div className="text-3xl font-bold text-red-600">0</div>
                <div className="text-sm font-medium text-red-700">{t('sms:sending.bulk.overdue')}</div>
                <div className="text-xs text-red-600 opacity-75">{t('sms:colorful.noOverduePayments')}</div>
              </div>
            )}

            {/* Total Eligible - Template SMS Button */}
            {totalEligibleUsers.length > 0 ? (
              <ColorfulTemplateSmsDialog
                users={templateUsers}
                trigger={
                  <Button
                    variant="outline"
                    className="h-auto p-6 flex flex-col items-center space-y-2 bg-gradient-to-br from-green-50 to-emerald-100 hover:from-green-100 hover:to-emerald-200 border-green-200 hover:border-green-300 transition-all duration-300 shadow-sm hover:shadow-md"
                  >
                    <div className="text-3xl font-bold text-green-600">{totalEligibleUsers.length}</div>
                    <div className="text-sm font-medium text-green-700">{t('sms:sending.bulk.totalEligible')}</div>
                    <div className="text-xs text-green-600 opacity-75">📢 {t('sms:colorful.clickForTemplateSms')}</div>
                  </Button>
                }
              />
            ) : (
              <div className="h-auto p-6 flex flex-col items-center space-y-2 bg-green-50 rounded-lg border border-green-200 opacity-50">
                <div className="text-3xl font-bold text-green-600">0</div>
                <div className="text-sm font-medium text-green-700">{t('sms:sending.bulk.totalEligible')}</div>
                <div className="text-xs text-green-600 opacity-75">{t('sms:colorful.noEligibleUsers')}</div>
              </div>
            )}
          </div>



          {/* Important Notes */}
          {eligiblePayments.length > 0 && (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                <div className="space-y-1">
                  <p className="font-medium">{t('sms:sending.bulk.notes.title')}</p>
                  <ul className="text-xs space-y-1 ml-4">
                    <li>• {t('sms:sending.bulk.notes.items.0')}</li>
                    <li>• {t('sms:sending.bulk.notes.items.1')}</li>
                    <li>• {t('sms:sending.bulk.notes.items.2')}</li>
                    <li>• {t('sms:sending.bulk.notes.items.3')}</li>
                  </ul>
                </div>
              </AlertDescription>
            </Alert>
          )}

          {/* No Eligible Payments */}
          {eligiblePayments.length === 0 && (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                {t('sms:sending.bulk.noEligible')}
              </AlertDescription>
            </Alert>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
