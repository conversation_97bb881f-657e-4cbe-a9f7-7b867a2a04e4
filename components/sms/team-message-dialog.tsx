'use client';

import { useState, useTransition } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON>Header,
  <PERSON>alogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Checkbox } from '@/components/ui/checkbox';
import { Loader2, MessageSquare, AlertTriangle} from 'lucide-react';
import { sendTeamSms } from '@/lib/actions/sms';
import { Athlete, Team } from '@/lib/types';
import { useSafeTranslation } from '@/hooks/use-safe-translation';
import {useToast} from "@/hooks/use-toast";
import { useSmsRefreshSafe } from '@/contexts/sms-context';

const teamMessageSchema = z.object({
  message: z.string().min(10, 'Message must be at least 10 characters').max(1000, 'Message must be 1000 characters or less'),
  sendToAll: z.boolean(),
  selectedAthleteIds: z.array(z.string()).optional(),
});

type TeamMessageFormData = z.infer<typeof teamMessageSchema>;

interface TeamMessageDialogProps {
  team: Team;
  athletes: Athlete[];
  trigger?: React.ReactNode;
  onSuccess?: () => void;
}

export default function TeamMessageDialog({
  team,
  athletes,
  trigger,
  onSuccess
}: TeamMessageDialogProps) {
  const { t } = useSafeTranslation();
  const { toast } = useToast();
  const { onSmsSuccess } = useSmsRefreshSafe();
  const [open, setOpen] = useState(false);
  const [isPending, startTransition] = useTransition();
  const [selectedAthletes, setSelectedAthletes] = useState<string[]>([]);

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    setValue,
    reset
  } = useForm<TeamMessageFormData>({
    resolver: zodResolver(teamMessageSchema),
    defaultValues: {
      message: '',
      sendToAll: true,
      selectedAthleteIds: [],
    }
  });

  const message = watch('message');
  const sendToAll = watch('sendToAll');
  const messageLength = message?.length || 0;

  // Calculate SMS count based on message length
  const calculateSmsCount = (length: number) => {
    if (length === 0) return 0;
    if (length <= 160) return 1;
    if (length <= 306) return 2;
    if (length <= 459) return 3;
    if (length <= 612) return 4;
    return Math.ceil(length / 153); // For messages longer than 612 chars
  };

  const smsCount = calculateSmsCount(messageLength);

  // Filter athletes with valid phone numbers
  const eligibleAthletes = athletes.filter(athlete => athlete.parentPhone);

  const toggleAthlete = (athleteId: string) => {
    setSelectedAthletes(prev => {
      if (prev.includes(athleteId)) {
        return prev.filter(id => id !== athleteId);
      } else {
        return [...prev, athleteId];
      }
    });
  };

  const selectAllAthletes = () => {
    setSelectedAthletes(eligibleAthletes.map(athlete => athlete.id));
  };

  const clearAthleteSelection = () => {
    setSelectedAthletes([]);
  };

  const onSubmit = (data: TeamMessageFormData) => {
    startTransition(async () => {
      try {
        const result = await sendTeamSms({
          teamId: team.id,
          message: data.message,
          athleteIds: data.sendToAll ? undefined : selectedAthletes,
        });

        if (result.success) {
          const sentCount = result.data?.sentCount || 0;
          toast({
            title: t('common.success'),
            description:sentCount === 1
                ? t('sms:sending.teamMessage.successSingle')
                : t('sms:sending.teamMessage.successPlural').replace(/{count}/g, sentCount.toString())
          });
          setOpen(false);
          reset();
          onSuccess?.();
          onSmsSuccess(); // Refresh balance and logs (safe - no-op if SMS context not available)
        }else{
          let errorDescriptionKey = '';
          if(result.errorType == 'BusinessRuleError'){
            errorDescriptionKey = `errors.${result.error}`;
          }else{
            errorDescriptionKey = 'sms:sending.teamMessage.error';
          }
          toast({
            title: t('common.error'),
            description:t(errorDescriptionKey),
            variant: "destructive",
          });
        }
      } catch (error) {
        console.error('Error sending team SMS:', error);
        toast({
          title: t('common.error'),
          description:t('sms:sending.teamMessage.error'),
          variant: "destructive",
        });
      }
    });
  };

  const handleClose = () => {
    setOpen(false);
    reset();
    setSelectedAthletes([]);
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button variant="outline">
            <MessageSquare className="h-4 w-4 mr-2" />
            {t('sms:actions.sendTeamSms')}
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>{t('sms:sending.teamMessage.title')}</DialogTitle>
          <DialogDescription>
            {t('sms:sending.teamMessage.description').replace('{teamName}', team.name)}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Team Info */}
          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-medium">{team.name}</h4>
              <p className="text-sm text-muted-foreground">
                {eligibleAthletes.length === 1
                  ? t('sms:sending.teamMessage.athletesWithPhoneSingle')
                  : t('sms:sending.teamMessage.athletesWithPhonePlural').replace(/{count}/g, eligibleAthletes.length.toString())
                }
              </p>
            </div>
            <Badge variant="secondary">
              {team.school?.name || 'No School'}
            </Badge>
          </div>

          {/* Message */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label htmlFor="message">{t('sms:sending.teamMessage.message')}</Label>
              <div className="flex items-center space-x-2 text-xs">
                <span className="text-muted-foreground">
                  {messageLength} {t('sms:common.characters')}
                </span>
                <span className={`font-medium ${smsCount > 1 ? 'text-orange-600' : 'text-green-600'}`}>
                  {smsCount} SMS{smsCount !== 1 ? 's' : ''}
                </span>
              </div>
            </div>
            <Textarea
              id="message"
              {...register('message')}
              placeholder={t('sms:sending.teamMessage.messagePlaceholder')}
              rows={4}
              className={errors.message ? 'border-red-500' : ''}
            />
            {errors.message && (
              <p className="text-sm text-red-500">{errors.message.message}</p>
            )}
            <p className="text-xs text-muted-foreground">
              Keep messages under 160 characters for single SMS. Longer messages may be split.
            </p>
          </div>

          {/* Recipient Selection */}
          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="sendToAll"
                checked={sendToAll}
                onCheckedChange={(checked) => {
                  setValue('sendToAll', !!checked);
                  if (checked) {
                    clearAthleteSelection();
                  }
                }}
              />
              <Label htmlFor="sendToAll">{t('sms:sending.teamMessage.sendToAll')}</Label>
            </div>

            {!sendToAll && (
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label>{t('sms:sending.teamMessage.selectRecipients')}</Label>
                  <div className="flex items-center space-x-2">
                    <Button type="button" variant="outline" size="sm" onClick={selectAllAthletes}>
                      {t('sms:sending.teamMessage.selectAll')}
                    </Button>
                    <Button type="button" variant="outline" size="sm" onClick={clearAthleteSelection}>
                      {t('sms:sending.teamMessage.clear')}
                    </Button>
                  </div>
                </div>
                <div className="max-h-40 overflow-y-auto border rounded-md p-3 space-y-2">
                  {eligibleAthletes.length === 0 ? (
                    <p className="text-sm text-muted-foreground">{t('sms:sending.teamMessage.noAthletes')}</p>
                  ) : (
                    eligibleAthletes.map((athlete) => (
                      <div key={athlete.id} className="flex items-center space-x-2">
                        <Checkbox
                          id={`athlete-${athlete.id}`}
                          checked={selectedAthletes.includes(athlete.id)}
                          onCheckedChange={() => toggleAthlete(athlete.id)}
                        />
                        <Label htmlFor={`athlete-${athlete.id}`} className="text-sm">
                          {athlete.name} {athlete.surname}
                        </Label>
                      </div>
                    ))
                  )}
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">Selected:</span>
                  <Badge variant="secondary">
                    {selectedAthletes.length} of {eligibleAthletes.length}
                  </Badge>
                </div>
              </div>
            )}
          </div>

          {/* Warning */}
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              {(() => {
                const recipientCount = sendToAll ? eligibleAthletes.length : selectedAthletes.length;
                const totalSmsCount = recipientCount * smsCount;

                if (recipientCount === 0) return t('sms:sending.teamMessage.noRecipients');

                // Ensure we have valid numbers to avoid NaN or undefined issues
                const safeRecipientCount = Math.max(0, recipientCount || 0);
                const safeSmsCount = Math.max(1, smsCount || 1);
                const safeTotalSmsCount = safeRecipientCount * safeSmsCount;

                return (
                  <div className="space-y-1">
                    <p>
                      {safeRecipientCount === 1
                        ? t('sms:sending.teamMessage.warningRecipientSingle')
                        : t('sms:sending.teamMessage.warningRecipientPlural').replace(/{count}/g, safeRecipientCount.toString())
                      }
                    </p>
                    <p className="text-sm">
                      {(() => {
                        try {
                          if (safeSmsCount === 1) {
                            return t('sms:sending.teamMessage.warningSmsCountSingle').replace('{total}', safeTotalSmsCount.toString());
                          } else {
                            return t('sms:sending.teamMessage.warningSmsCountPlural')
                              .replace('{smsPerRecipient}', safeSmsCount.toString())
                              .replace('{total}', safeTotalSmsCount.toString());
                          }
                        } catch (error) {
                          // Fallback to generic message if translation fails
                          console.warn('SMS translation error:', error);
                          return `Total: ${safeTotalSmsCount} SMS credits will be consumed from your balance.`;
                        }
                      })()}
                    </p>
                    {safeSmsCount > 1 && (
                      <p className="text-xs text-orange-600">
                        {t('sms:sending.teamMessage.warningLongMessage')}
                      </p>
                    )}
                  </div>
                );
              })()}
            </AlertDescription>
          </Alert>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={handleClose} disabled={isPending}>
              {t('sms:common.cancel')}
            </Button>
            <Button 
              type="submit" 
              disabled={isPending || (sendToAll ? eligibleAthletes.length === 0 : selectedAthletes.length === 0)}
            >
              {isPending && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              <MessageSquare className="mr-2 h-4 w-4" />
              {t('sms:common.send')} SMS ({sendToAll ? eligibleAthletes.length : selectedAthletes.length})
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
