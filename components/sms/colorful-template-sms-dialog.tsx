'use client';

import { useState, useTransition, useEffect, useMemo, useCallback } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Checkbox } from '@/components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Loader2, MessageSquare, Users, Eye, EyeOff, FileText, Send, CheckCircle, AlertTriangle } from 'lucide-react';
import { useSafeTranslation } from '@/hooks/use-safe-translation';
import { useToast } from '@/hooks/use-toast';
import { sendTemplateSms, getSmsTemplateByType } from '@/lib/actions/sms';
import { useSmsRefresh } from '@/contexts/sms-context';

const templateSmsSchema = z.object({
  templateType: z.enum(['general', 'event', 'meeting', 'holiday', 'custom']),
  useCustomTemplate: z.boolean(),
  customTemplate: z.string().max(1000, 'Template must be 1000 characters or less').optional(),
  selectedUserIds: z.array(z.string()).min(1, 'At least one user must be selected'),
});

type TemplateSmsFormData = z.infer<typeof templateSmsSchema>;

interface User {
  id: string;
  name: string;
  surname: string;
  parentPhone?: string;
  teamName?: string;
  status?: string;
}

interface ColorfulTemplateSmsDialogProps {
  users: User[];
  trigger?: React.ReactNode;
  onSuccess?: () => void;
}

export default function ColorfulTemplateSmsDialog({
  users,
  trigger,
  onSuccess
}: ColorfulTemplateSmsDialogProps) {
  const { t, i18n } = useSafeTranslation();
  const { toast } = useToast();
  const { onSmsSuccess } = useSmsRefresh();
  const [open, setOpen] = useState(false);
  const [isPending, startTransition] = useTransition();
  const [currentStep, setCurrentStep] = useState(1);
  const [previewTemplate, setPreviewTemplate] = useState(false);
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [dbTemplates, setDbTemplates] = useState<Record<string, string>>({});

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    setValue,
    reset
  } = useForm<TemplateSmsFormData>({
    resolver: zodResolver(templateSmsSchema),
    defaultValues: {
      templateType: 'general',
      useCustomTemplate: false,
      customTemplate: '',
      selectedUserIds: [],
    }
  });

  const templateType = watch('templateType');
  const useCustomTemplate = watch('useCustomTemplate');
  const customTemplate = watch('customTemplate');

  // Filter users that have phone numbers
  const eligibleUsers = useMemo(() => users.filter(user => user.parentPhone), [users]);

  useEffect(() => {
    // Only set selected users when dialog opens and we have eligible users
    if (open && eligibleUsers.length > 0 && selectedUsers.length === 0) {
      const allUserIds = eligibleUsers.map(user => user.id);
      setSelectedUsers(allUserIds);
      setValue('selectedUserIds', allUserIds);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [open, eligibleUsers, setValue]); // Intentionally omit selectedUsers.length to prevent infinite loop

  // Load database templates
  const loadDatabaseTemplates = useCallback(async () => {
    console.log('🔄 Loading database templates...', { language: i18n.language });

    const templateTypes: Array<'general' | 'event' | 'meeting' | 'holiday' | 'custom'> =
      ['general', 'event', 'meeting', 'holiday', 'custom'];

    const templates: Record<string, string> = {};

    for (const type of templateTypes) {
      if (type === 'custom') continue; // Skip custom type

      try {
        console.log(`📋 Loading template: ${type} (${i18n.language})`);
        const result = await getSmsTemplateByType(type, i18n.language || 'en');
        console.log(`📋 Template result for ${type}:`, result);

        if (result.success && result.data) {
          templates[type] = result.data.template;
          console.log(`✅ Loaded template ${type}:`, result.data.template.substring(0, 50) + '...');
        } else {
          console.warn(`⚠️ No template found for ${type} in ${i18n.language}`);
        }
      } catch (error) {
        console.error(`❌ Failed to load template for type ${type}:`, error);
      }
    }

    console.log('📋 Final templates loaded:', Object.keys(templates));
    setDbTemplates(templates);
  }, [i18n.language]);

  // Load a single template
  const loadSingleTemplate = useCallback(async (type: 'general' | 'event' | 'meeting' | 'holiday' | 'custom') => {
    if (type === 'custom') return;

    try {
      console.log(`📋 Loading single template: ${type} (${i18n.language})`);
      const result = await getSmsTemplateByType(type, i18n.language || 'en');
      console.log(`📋 Single template result for ${type}:`, result);

      if (result.success && result.data) {
        setDbTemplates(prev => ({
          ...prev,
          [type]: result.data.template
        }));
        console.log(`✅ Loaded single template ${type}:`, result.data.template.substring(0, 50) + '...');
      } else {
        console.warn(`⚠️ No template found for ${type} in ${i18n.language}`);
      }
    } catch (error) {
      console.error(`❌ Failed to load single template for type ${type}:`, error);
    }
  }, [i18n.language]);

  // Load database templates when dialog opens or language changes
  useEffect(() => {
    if (open) {
      loadDatabaseTemplates();
    }
  }, [open, i18n.language, loadDatabaseTemplates]);

  // Reload templates when template type changes
  useEffect(() => {
    if (open && templateType && !useCustomTemplate) {
      console.log(`🔄 Template type changed to: ${templateType}`);
      // If we don't have this template loaded, try to load it
      if (!dbTemplates[templateType]) {
        loadSingleTemplate(templateType);
      }
    }
  }, [templateType, useCustomTemplate, open, dbTemplates, loadSingleTemplate]);

  const handleUserSelection = (userId: string, checked: boolean) => {
    const newSelection = checked 
      ? [...selectedUsers, userId]
      : selectedUsers.filter(id => id !== userId);
    
    setSelectedUsers(newSelection);
    setValue('selectedUserIds', newSelection);
  };

  const handleSelectAll = () => {
    const allUserIds = eligibleUsers.map(user => user.id);
    setSelectedUsers(allUserIds);
    setValue('selectedUserIds', allUserIds);
  };

  const handleSelectNone = () => {
    setSelectedUsers([]);
    setValue('selectedUserIds', []);
  };

  const nextStep = () => {
    if (currentStep < 3) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleClose = () => {
    setOpen(false);
    setCurrentStep(1);
    setShowConfirmation(false);
    setSelectedUsers([]);
    setPreviewTemplate(false);
    reset();
  };

  const getStepColor = (step: number) => {
    if (step < currentStep) return 'bg-green-500 text-white border-green-500';
    if (step === currentStep) return 'bg-purple-500 text-white border-purple-500';
    return 'bg-gray-200 text-gray-600 border-gray-300';
  };

  const getStepIcon = (step: number) => {
    if (step < currentStep) return <CheckCircle className="w-5 h-5" />;
    if (step === 1) return <Users className="w-5 h-5" />;
    if (step === 2) return <FileText className="w-5 h-5" />;
    if (step === 3) return <Send className="w-5 h-5" />;
    return <div className="w-5 h-5 rounded-full bg-current" />;
  };

  const getTemplateText = () => {
    if (useCustomTemplate) {
      return customTemplate || '';
    }

    // Use ONLY database templates, no fallback to translation files
    const template = dbTemplates[templateType] || '';
    console.log(`📝 Getting template for ${templateType}:`, template ? template.substring(0, 50) + '...' : 'EMPTY');
    return template;
  };

  const getPreviewText = () => {
    const template = getTemplateText();
    return template
      .replace(/\{\{parentName\}\}/g, t('sms:configuration.preview.parentName'))
      .replace(/\{\{athleteName\}\}/g, t('sms:configuration.preview.athleteName'))
      .replace(/\{\{schoolName\}\}/g, t('sms:configuration.preview.schoolName'))
      .replace(/\{\{teamName\}\}/g, t('sms:configuration.preview.teamName'));
  };

  const calculateSmsCount = (text: string) => {
    if (!text) return 0;
    const length = text.length;
    if (length <= 160) return 1;
    if (length <= 306) return 2;
    if (length <= 459) return 3;
    if (length <= 612) return 4;
    return Math.ceil(length / 153); // For messages longer than 612 chars
  };

  const onSubmit = (data: TemplateSmsFormData) => {
    if (selectedUsers.length === 0) {
      toast({
        title: t('sms:common.error'),
        description: t('sms:common.pleaseSelectAtLeastOneUser'),
        variant: 'destructive',
      });
      return;
    }

    setShowConfirmation(true);
  };

  const confirmSend = () => {
    startTransition(async () => {
      try {
        const result = await sendTemplateSms({
          templateType,
          customTemplate: useCustomTemplate ? customTemplate : undefined,
          athleteIds: selectedUsers,
          language: i18n.language || 'en',
        });

        if (result.success) {
          const sentCount = result.data?.sentCount || 0;
          toast({
            title: t('sms:common.success'),
            description: sentCount === 1
              ? t('sms:sending.templateSms.successSingle')
              : t('sms:sending.templateSms.successPlural').replace(/{count}/g, sentCount.toString()),
          });

          handleClose();
          onSuccess?.();
          onSmsSuccess(); // Refresh balance and logs
        } else {
          toast({
            title: t('sms:common.error'),
            description: result.error || t('sms:common.failedToSendTemplateSms'),
            variant: 'destructive',
          });
        }
      } catch (error) {
        console.error('Error sending template SMS:', error);
        toast({
          title: t('sms:common.error'),
          description: t('sms:common.failedToSendTemplateSms'),
          variant: 'destructive',
        });
      }
    });
  };

  if (eligibleUsers.length === 0) {
    return null;
  }

  const templateText = getTemplateText();
  const smsCount = calculateSmsCount(templateText);
  const totalSmsCredits = smsCount * selectedUsers.length;

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button>
            <MessageSquare className="mr-2 h-4 w-4" />
            {t('sms:templates.title')}
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="max-w-5xl max-h-[90vh] flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle className="flex items-center space-x-2">
            <MessageSquare className="h-6 w-6 text-purple-600" />
            <span className="text-xl font-bold text-gray-800">
              {t('sms:templates.title')}
            </span>
          </DialogTitle>
          
          {/* Colorful Step Indicator */}
          <div className="flex items-center justify-center space-x-6 mt-6 mb-4 p-4 bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg">
            <div className="flex items-center space-x-3">
              <div className={`w-12 h-12 rounded-full flex items-center justify-center border-2 transition-all duration-300 ${getStepColor(1)}`}>
                {getStepIcon(1)}
              </div>
              <div className="text-left">
                <div className={`text-sm font-bold ${currentStep === 1 ? 'text-purple-600' : currentStep > 1 ? 'text-green-600' : 'text-gray-500'}`}>
                  {t('sms:steps.step')} 1
                </div>
                <div className={`text-xs ${currentStep === 1 ? 'text-purple-600' : currentStep > 1 ? 'text-green-600' : 'text-gray-500'}`}>
                  {t('sms:steps.selectRecipients')}
                </div>
              </div>
            </div>
            
            <div className={`w-12 h-1 rounded-full transition-all duration-300 ${currentStep > 1 ? 'bg-green-500' : 'bg-gray-300'}`}></div>
            
            <div className="flex items-center space-x-3">
              <div className={`w-12 h-12 rounded-full flex items-center justify-center border-2 transition-all duration-300 ${getStepColor(2)}`}>
                {getStepIcon(2)}
              </div>
              <div className="text-left">
                <div className={`text-sm font-bold ${currentStep === 2 ? 'text-purple-600' : currentStep > 2 ? 'text-green-600' : 'text-gray-500'}`}>
                  {t('sms:steps.step')} 2
                </div>
                <div className={`text-xs ${currentStep === 2 ? 'text-purple-600' : currentStep > 2 ? 'text-green-600' : 'text-gray-500'}`}>
                  {t('sms:steps.customizeMessage')}
                </div>
              </div>
            </div>
            
            <div className={`w-12 h-1 rounded-full transition-all duration-300 ${currentStep > 2 ? 'bg-green-500' : 'bg-gray-300'}`}></div>
            
            <div className="flex items-center space-x-3">
              <div className={`w-12 h-12 rounded-full flex items-center justify-center border-2 transition-all duration-300 ${getStepColor(3)}`}>
                {getStepIcon(3)}
              </div>
              <div className="text-left">
                <div className={`text-sm font-bold ${currentStep === 3 ? 'text-purple-600' : 'text-gray-500'}`}>
                  {t('sms:steps.step')} 3
                </div>
                <div className={`text-xs ${currentStep === 3 ? 'text-purple-600' : 'text-gray-500'}`}>
                  {t('sms:steps.reviewAndSend')}
                </div>
              </div>
            </div>
          </div>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto">
          <form id="template-sms-form" onSubmit={handleSubmit(onSubmit)} className="space-y-6">

            {/* Step 1: Select Recipients */}
            {currentStep === 1 && (
              <div className="space-y-6 p-6 bg-gradient-to-br from-purple-50 to-pink-50 rounded-lg border border-purple-200">
                <div className="text-center">
                  <Users className="w-16 h-16 text-purple-500 mx-auto mb-4" />
                  <h3 className="text-lg font-bold text-purple-800 mb-2">{t('sms:templates.form.selectUsers')}</h3>
                  <p className="text-purple-600 text-sm">{t('sms:templates.description')}</p>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <Badge variant="outline" className="bg-white border-purple-300 text-purple-700">
                        {selectedUsers.length} / {eligibleUsers.length} {t('sms:common.selected')}
                      </Badge>
                    </div>
                    <div className="flex space-x-2">
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={handleSelectAll}
                        className="bg-white border-purple-300 text-purple-700 hover:bg-purple-50"
                      >
                        {t('sms:common.selectAll')}
                      </Button>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={handleSelectNone}
                        className="bg-white border-purple-300 text-purple-700 hover:bg-purple-50"
                      >
                        {t('sms:common.selectNone')}
                      </Button>
                    </div>
                  </div>

                  <div className="max-h-64 overflow-y-auto bg-white rounded-lg border border-purple-200 p-4 space-y-3">
                    {eligibleUsers.map((user) => (
                      <div
                        key={user.id}
                        className={`flex items-center space-x-3 p-3 rounded-lg transition-colors ${
                          selectedUsers.includes(user.id)
                            ? 'bg-purple-100 border border-purple-300 shadow-sm'
                            : 'hover:bg-purple-50'
                        }`}
                      >
                        <Checkbox
                          key={`checkbox-${user.id}-${selectedUsers.includes(user.id)}`}
                          id={`user-${user.id}`}
                          checked={selectedUsers.includes(user.id)}
                          onCheckedChange={(checked) => handleUserSelection(user.id, checked as boolean)}
                          className="border-purple-300"
                        />
                        <label
                          htmlFor={`user-${user.id}`}
                          className="flex-1 flex items-center justify-between text-sm cursor-pointer"
                        >
                          <div className="flex items-center space-x-3">
                            <div>
                              <div className="font-medium text-gray-800">
                                {user.name} {user.surname}
                              </div>
                              <div className="text-xs text-gray-500">
                                {user.parentPhone || 'No phone number'}
                              </div>
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            {user.teamName && (
                              <Badge variant="secondary" className="text-xs">
                                {user.teamName}
                              </Badge>
                            )}
                            {user.status && (
                              <Badge
                                variant={user.status === 'active' ? 'default' : 'secondary'}
                                className="text-xs"
                              >
                                {t(`common.status.${user.status}`)}
                              </Badge>
                            )}
                          </div>
                        </label>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* Step 2: Customize Message */}
            {currentStep === 2 && (
              <div className="space-y-6 p-6 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-lg border border-blue-200">
                <div className="text-center">
                  <FileText className="w-16 h-16 text-blue-500 mx-auto mb-4" />
                  <h3 className="text-lg font-bold text-blue-800 mb-2">{t('sms:colorful.customizeYourMessage')}</h3>
                  <p className="text-blue-600 text-sm">{t('sms:colorful.chooseTemplate')}</p>
                </div>

                <div className="space-y-4">
                  {/* Template Type Selection */}
                  <div className="space-y-2">
                    <Label className="text-sm font-medium text-blue-800">{t('sms:templates.form.templateType')}</Label>
                    <Select
                      value={templateType}
                      onValueChange={(value) => setValue('templateType', value as any)}
                    >
                      <SelectTrigger className="bg-white border-blue-300">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="general">{t('sms:templates.types.general')}</SelectItem>
                        <SelectItem value="event">{t('sms:templates.types.event')}</SelectItem>
                        <SelectItem value="meeting">{t('sms:templates.types.meeting')}</SelectItem>
                        <SelectItem value="holiday">{t('sms:templates.types.holiday')}</SelectItem>
                        <SelectItem value="custom">{t('sms:templates.types.custom')}</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Custom Template Toggle */}
                  {templateType !== 'custom' && (
                    <div className="flex items-center space-x-3 p-4 bg-white rounded-lg border border-blue-200">
                      <Checkbox
                        id="useCustomTemplate"
                        checked={useCustomTemplate}
                        onCheckedChange={(checked) => setValue('useCustomTemplate', checked as boolean)}
                        className="border-blue-300"
                      />
                      <Label htmlFor="useCustomTemplate" className="text-sm font-medium text-blue-700">
                        {t('sms:templates.form.useCustom')}
                      </Label>
                    </div>
                  )}

                  {/* Template Content */}
                  {(useCustomTemplate || templateType === 'custom') ? (
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <Label className="text-sm font-medium text-blue-800">{t('sms:templates.form.customMessage')}</Label>
                        <div className="flex items-center space-x-2 text-xs">
                          <span className="text-gray-500">
                            {templateText.length} {t('sms:common.characters')}
                          </span>
                          <span className={`font-medium ${smsCount > 1 ? 'text-orange-600' : 'text-green-600'}`}>
                            ~{smsCount} SMS
                          </span>
                        </div>
                      </div>
                      <Textarea
                        {...register('customTemplate')}
                        placeholder={t('sms:templates.form.customPlaceholder')}
                        className="min-h-[120px] bg-white border-blue-300"
                      />
                      {errors.customTemplate && (
                        <p className="text-sm text-red-600">{errors.customTemplate.message}</p>
                      )}
                    </div>
                  ) : (
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <Label className="text-sm font-medium text-blue-800">{t('sms:common.defaultTemplate')}</Label>
                        <div className="flex items-center space-x-2 text-xs">
                          <span className="text-gray-500">
                            {templateText.length} {t('sms:common.characters')}
                          </span>
                          <span className={`font-medium ${smsCount > 1 ? 'text-orange-600' : 'text-green-600'}`}>
                            ~{smsCount} SMS
                          </span>
                        </div>
                      </div>
                      <div className="p-4 bg-white rounded-lg border border-blue-200">
                        <p className="text-sm text-gray-700">{templateText}</p>
                      </div>
                    </div>
                  )}

                  {/* Preview */}
                  {templateText && (
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <Label className="text-sm font-medium text-blue-800">{t('sms:common.preview')}</Label>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => setPreviewTemplate(!previewTemplate)}
                          className="text-blue-600 hover:text-blue-800"
                        >
                          {previewTemplate ? (
                            <>
                              <EyeOff className="h-4 w-4 mr-2" />
                              {t('sms:colorful.hidePreview')}
                            </>
                          ) : (
                            <>
                              <Eye className="h-4 w-4 mr-2" />
                              {t('sms:colorful.showPreview')}
                            </>
                          )}
                        </Button>
                      </div>

                      {previewTemplate && (
                        <div className="p-4 bg-white rounded-lg border border-blue-200">
                          <p className="text-sm text-gray-700 whitespace-pre-wrap">
                            {getPreviewText()}
                          </p>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Step 3: Review & Send */}
            {currentStep === 3 && (
              <div className="space-y-6 p-6 bg-gradient-to-br from-green-50 to-emerald-50 rounded-lg border border-green-200">
                <div className="text-center">
                  <Send className="w-16 h-16 text-green-500 mx-auto mb-4" />
                  <h3 className="text-lg font-bold text-green-800 mb-2">{t('sms:steps.reviewAndSend')}</h3>
                  <p className="text-green-600 text-sm">{t('sms:colorful.reviewBeforeSending')}</p>
                </div>

                <div className="space-y-4">
                  {/* Summary */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="bg-white p-4 rounded-lg border border-green-200 text-center">
                      <div className="text-2xl font-bold text-green-600">{selectedUsers.length}</div>
                      <div className="text-sm text-green-700">{t('sms:steps.recipients')}</div>
                    </div>
                    <div className="bg-white p-4 rounded-lg border border-green-200 text-center">
                      <div className="text-2xl font-bold text-green-600">{smsCount}</div>
                      <div className="text-sm text-green-700">{t('sms:colorful.smsPerRecipient')}</div>
                    </div>
                    <div className="bg-white p-4 rounded-lg border border-green-200 text-center">
                      <div className="text-2xl font-bold text-green-600">{totalSmsCredits}</div>
                      <div className="text-sm text-green-700">{t('sms:colorful.totalSmsCredits')}</div>
                    </div>
                  </div>

                  {/* Message Preview */}
                  <div className="bg-white p-4 rounded-lg border border-green-200">
                    <Label className="text-sm font-medium text-green-800 mb-2 block">{t('sms:colorful.messagePreview')}</Label>
                    <div className="p-3 bg-gray-50 rounded border">
                      <p className="text-sm text-gray-700 whitespace-pre-wrap">
                        {getPreviewText()}
                      </p>
                    </div>
                  </div>

                  {/* Warning */}
                  <Alert className="border-orange-200 bg-orange-50">
                    <AlertTriangle className="h-4 w-4 text-orange-600" />
                    <AlertDescription className="text-orange-800">
                      <div className="space-y-1">
                        <p className="font-medium">
                          {t('sms:colorful.sendImmediately')}
                        </p>
                        <p className="text-sm">
                          {t('sms:colorful.reviewCarefully')}
                        </p>
                      </div>
                    </AlertDescription>
                  </Alert>
                </div>
              </div>
            )}

          </form>
        </div>

        {/* Navigation Footer */}
        <DialogFooter className="flex-shrink-0 mt-4 pt-4 border-t bg-gray-50">
          <div className="flex justify-between w-full">
            <div className="flex space-x-2">
              {currentStep > 1 && (
                <Button
                  type="button"
                  variant="outline"
                  onClick={prevStep}
                  disabled={isPending}
                >
                  {t('sms:steps.previous')}
                </Button>
              )}
            </div>

            <div className="flex space-x-2">
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                disabled={isPending}
              >
                {t('sms:common.cancel')}
              </Button>

              {currentStep < 3 ? (
                <Button
                  type="button"
                  onClick={nextStep}
                  disabled={currentStep === 1 && selectedUsers.length === 0}
                  className="bg-purple-600 hover:bg-purple-700"
                >
                  {t('sms:steps.nextStep')}
                </Button>
              ) : (
                <Button
                  type="submit"
                  form="template-sms-form"
                  disabled={isPending || selectedUsers.length === 0}
                  className="bg-green-600 hover:bg-green-700"
                >
                  {isPending && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  <Send className="mr-2 h-4 w-4" />
                  {t('sms:templates.form.sendTemplate')} ({selectedUsers.length})
                </Button>
              )}
            </div>
          </div>
        </DialogFooter>
      </DialogContent>

      {/* Confirmation Dialog */}
      <Dialog open={showConfirmation} onOpenChange={setShowConfirmation}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2">
              <AlertTriangle className="h-5 w-5 text-orange-500" />
              <span>{t('sms:steps.confirmation')}</span>
            </DialogTitle>
            <DialogDescription>
              {t('sms:steps.confirmMessage')}
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div className="p-4 bg-orange-50 rounded-lg border border-orange-200">
              <div className="text-sm space-y-2">
                <div className="flex justify-between">
                  <span className="text-gray-600">{t('sms:steps.recipients')}:</span>
                  <span className="font-medium">{selectedUsers.length}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">{t('sms:steps.smsCredits')}:</span>
                  <span className="font-medium">{totalSmsCredits}</span>
                </div>
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => setShowConfirmation(false)}
              disabled={isPending}
            >
              {t('sms:common.cancel')}
            </Button>
            <Button
              type="button"
              onClick={confirmSend}
              disabled={isPending}
              className="bg-red-600 hover:bg-red-700"
            >
              {isPending && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {t('sms:steps.yesSend')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Dialog>
  );
}
