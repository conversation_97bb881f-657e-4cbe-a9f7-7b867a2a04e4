"use client";

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Footer } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import { useSafeTranslation } from "@/hooks/use-safe-translation";
import { createRefund } from "@/lib/actions/payments";
import { RefreshCw } from "lucide-react";
import { Payment } from "@/lib/types";

interface RefundDialogProps {
  payment: Payment;
  trigger: React.ReactNode;
  onRefundCreated?: () => void;
}

export default function RefundDialog({ payment, trigger, onRefundCreated }: RefundDialogProps) {
  const { t } = useSafeTranslation();
  const { toast } = useToast();
  const [open, setOpen] = useState(false);
  const [amount, setAmount] = useState('');
  const [reason, setReason] = useState('');
  const [isCreating, setIsCreating] = useState(false);

  const maxRefundAmount = parseFloat(payment.amountPaid || '0');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const refundAmount = parseFloat(amount);
    
    // Validate amount
    if (isNaN(refundAmount) || refundAmount <= 0) {
      toast({
        title: t('common.validation.invalidAmount'),
        variant: "destructive",
      });
      return;
    }

    if (refundAmount > maxRefundAmount) {
      toast({
        title: t('payments.businessRules.errors.refundTooLarge'),
        description: t('payments.refund.maxAmount', { amount: maxRefundAmount.toFixed(2) }),
        variant: "destructive",
      });
      return;
    }

    if (!reason.trim()) {
      toast({
        title: t('common.validation.required', { field: t('payments.refund.reason') }),
        variant: "destructive",
      });
      return;
    }

    setIsCreating(true);
    try {
      const result = await createRefund(payment.id, {
        amount: refundAmount.toString(),
        reason: reason.trim()
      });

      if (result.success) {
        toast({
          title: t('payments.refund.success'),
          description: t('payments.refund.successDetail', { 
            amount: refundAmount.toFixed(2),
            currency: t('common.currency')
          }),
        });

        setOpen(false);
        setAmount('');
        setReason('');
        onRefundCreated?.();
      } else {
        console.error("Error creating refund:", result.error);
        let errorDescriptionKey = '';
        if(result.errorType == 'BusinessRuleError'){
          errorDescriptionKey = `errors.${result.error}`;
        }else{
          errorDescriptionKey = 'payments.refund.createErrorDescription';
        }
        
        toast({
          title: t('payments.refund.error'),
          description: t(errorDescriptionKey),
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: t('payments.refund.error'),
        description: error instanceof Error ? error.message : 'Unknown error',
        variant: "destructive",
      });
    } finally {
      setIsCreating(false);
    }
  };

  return (
    <>
      <div onClick={() => setOpen(true)}>
        {trigger}
      </div>
      
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <RefreshCw className="h-5 w-5" />
              {t('payments.refund.title')}
            </DialogTitle>
          </DialogHeader>
          
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <p className="text-sm text-muted-foreground">
                {t('payments.refund.description')}
              </p>
              <p className="text-xs text-muted-foreground">
                {t('payments.refund.maxAmount', { amount: maxRefundAmount.toFixed(2) })}
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="refund-amount">{t('payments.refund.amount')}</Label>
              <Input
                id="refund-amount"
                type="number"
                step="0.01"
                min="0.01"
                max={maxRefundAmount}
                value={amount}
                onChange={(e) => setAmount(e.target.value)}
                placeholder="0.00"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="refund-reason">{t('payments.refund.reason')}</Label>
              <Textarea
                id="refund-reason"
                value={reason}
                onChange={(e) => setReason(e.target.value)}
                placeholder={t('payments.refund.reasonPlaceholder')}
                rows={3}
                required
              />
            </div>

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => setOpen(false)}
                disabled={isCreating}
              >
                {t('common.actions.cancel')}
              </Button>
              <Button
                type="submit"
                disabled={isCreating}
              >
                {isCreating ? t('common.actions.processing') : t('payments.refund.create')}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </>
  );
}
