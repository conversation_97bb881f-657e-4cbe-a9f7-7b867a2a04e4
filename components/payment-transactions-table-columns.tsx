"use client";

import { ColumnDef } from "@tanstack/react-table";
import { format, isValid, parseISO } from "date-fns";
import { PaymentTransaction } from "@/lib/types";
import { Badge } from "@/components/ui/badge";
import Link from "next/link";
import { useSafeTranslation } from "@/hooks/use-safe-translation";
import { formatTransactionNotes } from "@/lib/utils/transaction-notes";

// Move cell render functions outside of the columns definition
const AthleteCell = ({ transaction }: { transaction: PaymentTransaction }) => {
  const { t } = useSafeTranslation();
  if (!transaction.athlete) {
    return <span className="text-muted-foreground">{t('payments.messages.unknownAthlete')}</span>;
  }
  return (
    <div className="space-y-1">
      <div className="font-medium">
        {transaction.athlete.name} {transaction.athlete.surname}
      </div>
      {transaction.athlete.parentPhone && (
        <div className="text-sm text-muted-foreground">
          {transaction.athlete.parentPhone}
        </div>
      )}
    </div>
  );
};

const AmountCell = ({ amount }: { amount: string }) => {
  const { t } = useSafeTranslation();
  const formattedAmount = parseFloat(amount);
  return (
    <div className="font-medium">{formattedAmount.toFixed(2)} {t('common.currency')}</div>
  );
};

const DateCell = ({ date }: { date: Date }) => {
  const { t } = useSafeTranslation();
  if (!date || !isValid(date)) {
    return <span className="text-muted-foreground">{t('payments.dates.invalidDate')}</span>;
  }
  return (
    <div className="text-sm">
      {format(date, 'dd/MM/yyyy HH:mm')}
    </div>
  );
};

const TransactionMethodCell = ({ method }: { method: string }) => {
  const { t } = useSafeTranslation();
  const methodColors = {
    cash: "bg-green-100 text-green-800",
    bank_transfer: "bg-blue-100 text-blue-800",
    credit_card: "bg-purple-100 text-purple-800",
    existing_balance: "bg-orange-100 text-orange-800",
  };
  
  return (
    <Badge className={methodColors[method as keyof typeof methodColors] || "bg-gray-100 text-gray-800"}>
      {t(`payments.transactionMethods.${method}`)}
    </Badge>
  );
};

const PaymentCell = ({ transaction }: { transaction: PaymentTransaction }) => {
  const { t } = useSafeTranslation();
  if (!transaction.payment) {
    return (
      <Badge variant="secondary">
        {t('payments.transactions.balanceTopUp')}
      </Badge>
    );
  }
  return (
    <div className="space-y-1">
      <div className="text-sm font-medium">
        {transaction.payment.description || `${t('payments.types.' + transaction.payment.type)} - ${transaction.payment.amount} ${t('common.currency')}`}
      </div>
      <Link 
        href={`/payments/${transaction.payment.id}`}
        className="text-xs text-blue-600 hover:text-blue-800"
      >
        {t('payments.actions.viewDetails')}
      </Link>
    </div>
  );
};

const NotesCell = ({ notes }: { notes?: string | null }) => {
  const { t } = useSafeTranslation();
  if (!notes) {
    return <span className="text-muted-foreground text-sm">-</span>;
  }

  const formattedNotes = formatTransactionNotes(notes, t);

  return (
    <div className="text-sm max-w-xs truncate" title={formattedNotes}>
      {formattedNotes}
    </div>
  );
};

export const createPaymentTransactionColumns = (t: any): ColumnDef<PaymentTransaction>[] => [
  {
    accessorKey: "athleteId",
    header: () => t('payments.transactions.athlete'),
    cell: ({ row }) => <AthleteCell transaction={row.original} />,
  },
  {
    accessorKey: "amount",
    header: () => t('payments.transactions.amount'),
    cell: ({ row }) => <AmountCell amount={row.original.amount} />,
  },
  {
    accessorKey: "transactionMethod",
    header: () => t('payments.transactions.method'),
    cell: ({ row }) => <TransactionMethodCell method={row.original.transactionMethod} />,
  },
  {
    accessorKey: "transactionDate",
    header: () => t('payments.transactions.date'),
    cell: ({ row }) => <DateCell date={row.original.transactionDate} />,
  },
  {
    accessorKey: "paymentId",
    header: () => t('payments.transactions.payment'),
    cell: ({ row }) => <PaymentCell transaction={row.original} />,
  },
  {
    accessorKey: "notes",
    header: () => t('payments.transactions.notes.label'),
    cell: ({ row }) => <NotesCell notes={row.getValue("notes")} />,
  },
];
