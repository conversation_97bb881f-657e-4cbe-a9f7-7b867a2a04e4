"use client";

import { ColumnDef } from "@tanstack/react-table";
import { format, isValid, parseISO } from "date-fns";
import { Payment } from "@/lib/types";
import { Button } from "@/components/ui/button";
import { BadgeStatus } from "@/components/ui/badge-status";
import Link from "next/link";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { MoreHorizontal, Pencil, CheckCircle, Eye, MessageSquare, RefreshCw, Trash2 } from "lucide-react";
import { useSafeTranslation } from "@/hooks/use-safe-translation";
import { useToast } from "@/hooks/use-toast";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { processPayment, deletePayment } from "@/lib/actions";
import PaymentReminderDialog from "@/components/sms/payment-reminder-dialog";
import RefundDialog from "@/components/payments/refund-dialog";
import { getPaymentPermissions } from "@/lib/utils/payment-business-rules";

// Delete Payment Component with AlertDialog
const DeletePaymentDialog = ({ payment, permissions }: { payment: Payment, permissions: any }) => {
  const { t } = useSafeTranslation();
  const { toast } = useToast();
  const router = useRouter();
  const [isDeleting, setIsDeleting] = useState(false);

  const handleDelete = async () => {
    setIsDeleting(true);
    try {
      await deletePayment(payment.id);
      
      toast({
        title: t('payments.messages.deleteSuccess'),
        variant: "default",
      });
      router.refresh();
    } catch (error) {
      toast({
        title: t('payments.messages.deleteError'),
        description: error instanceof Error ? error.message : t('payments.messages.deleteError'),
        variant: "destructive",
      });
    } finally {
      setIsDeleting(false);
    }
  };

  if (!permissions.canDelete) {
    return (
      <DropdownMenuItem disabled>
        <Trash2 className="mr-2 h-4 w-4 opacity-50" />
        <span className="opacity-50">{t('payments.actions.delete')}</span>
      </DropdownMenuItem>
    );
  }

  return (
    <AlertDialog>
      <AlertDialogTrigger asChild>
        <DropdownMenuItem
          onSelect={(e) => e.preventDefault()}
          className="text-red-600 focus:text-red-600"
        >
          <Trash2 className="mr-2 h-4 w-4" />
          {t('payments.actions.delete')}
        </DropdownMenuItem>
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>{t('payments.actions.deleteConfirm')}</AlertDialogTitle>
          <AlertDialogDescription>
            {t('common.actions.confirmDelete')}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>{t('common.actions.cancel')}</AlertDialogCancel>
          <AlertDialogAction
            onClick={handleDelete}
            className="bg-red-600 hover:bg-red-700"
            disabled={isDeleting}
          >
            {isDeleting ? t('common.actions.deleting') : t('common.actions.delete')}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};

// Move cell render functions outside of the columns definition
const AmountCell = ({ payment }: { payment: Payment }) => {
  const { t } = useSafeTranslation();
  const totalAmount = parseFloat(payment.amount);
  const paidAmount = parseFloat(payment.amountPaid || '0');
  const remainingAmount = totalAmount - paidAmount;
  const paymentPercentage = totalAmount > 0 ? (paidAmount / totalAmount) * 100 : 0;

  if (payment.status === 'partially_paid') {
    return (
      <div className="space-y-2">
        <div className="font-medium">{totalAmount.toFixed(2)} {t('common.currency')}</div>

        {/* Progress Bar */}
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className="bg-gradient-to-r from-green-400 to-green-600 h-2 rounded-full transition-all duration-300"
            style={{ width: `${paymentPercentage}%` }}
          ></div>
        </div>

        {/* Payment Details */}
        <div className="flex justify-between text-xs">
          <span className="text-green-600 font-medium">
            ✓ {paidAmount.toFixed(2)} {t('common.currency')}
          </span>
          <span className="text-orange-600 font-medium">
            {remainingAmount.toFixed(2)} {t('common.currency')} {t('payments.remaining')}
          </span>
        </div>

        {/* Percentage */}
        <div className="text-center">
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
            {paymentPercentage.toFixed(0)}% {t('payments.paid')}
          </span>
        </div>
      </div>
    );
  }

  return (
    <div className="font-medium">{totalAmount.toFixed(2)} {t('common.currency')}</div>
  );
};

const DateCell = ({ date, label, t }: { date: string | Date | null; label: string; t: any }) => {
  if (!date) {
    return (
      <div>
        <div className="text-xs text-muted-foreground">{label}</div>
        <div className="text-muted-foreground">{t('payments.dates.notSet')}</div>
      </div>
    );
  }

  let parsedDate: Date;
  if (typeof date === 'string') {
    parsedDate = parseISO(date);
    if (!isValid(parsedDate)) {
      return (
        <div>
          <div className="text-xs text-muted-foreground">{label}</div>
          <div className="text-muted-foreground">{t('payments.dates.invalidDate')}</div>
        </div>
      );
    }
  } else {
    parsedDate = date;
  }

  return (
    <div>
      <div className="text-xs text-muted-foreground">{label}</div>
      <div>{format(parsedDate, "dd/MM/yyyy")}</div>
    </div>
  );
};

const TypeCell = ({ type }: { type: string }) => {
  const { t } = useSafeTranslation();
  const typeKey = `payments.types.${type.toLowerCase()}`;
  return (
    <div className="capitalize">{t(typeKey)}</div>
  );
};


const DescriptionCell = ({ description }: { description: string }) => {
  const { t } = useSafeTranslation();
  
  if (!description) return <div>-</div>;
  
    // Check if the description is a translation key
    if (description.startsWith('descriptions.')) {
      const translationKey = `payments.${description}`;
      
      // Handle different types of description keys
      if (description === 'descriptions.initialBalance') {
        return <div>{t('payments.descriptions.initialBalance')}</div>;
      }
      
      if (description === 'descriptions.initialBalanceFromImport') {
        return <div>{t('payments.descriptions.initialBalanceFromImport')}</div>;
      }
      
      if (description === 'descriptions.proratedBalanceGeneric') {
        return <div>{t('payments.descriptions.proratedBalanceGeneric')}</div>;
      }
      
      if (description.startsWith('descriptions.proratedBalance')) {
        // For prorated balance, if we only have the key without plan name,
        // use a simpler description that doesn't require plan name interpolation
        return <div>{t('payments.descriptions.proratedBalanceGeneric')}</div>;
      }
      
      // Fallback: try to translate the key directly
      const translated = t(translationKey);
      if (translated !== translationKey) {
        return <div>{translated}</div>;
      }
    }  // Check for other common patterns and handle them
  if (description.includes('remaining days of the month')) {
    // This might be an already-interpolated prorated balance description
    // Try to detect the plan name pattern
    const match = description.match(/Prorated balance for (.+) \(remaining days of the month\)/);
    if (match) {
      const planName = match[1];
      return <div>{t('payments.descriptions.proratedBalance', { planName })}</div>;
    }
  }
  
  if (description === 'Initial balance') {
    return <div>{t('payments.descriptions.initialBalance')}</div>;
  }
  
  if (description === 'Initial balance from import') {
    return <div>{t('payments.descriptions.initialBalanceFromImport')}</div>;
  }
  
  // Return the original description if no translation pattern matches
  return <div>{description}</div>;
};

const AthleteCell = ({ payment }: { payment: Payment }) => {
  const { t } = useSafeTranslation();
  
  if (!payment.athlete) {
    return <div className="text-muted-foreground">{t('payments.messages.unknownAthlete')}</div>;
  }
  
  return (
    <div>
      <div className="font-medium">{payment.athlete.name} {payment.athlete.surname}</div>
      {payment.athlete.parentEmail && (
        <div className="text-xs text-muted-foreground">
          {payment.athlete.parentEmail}
        </div>
      )}
    </div>
  );
};

const ActionsCell = ({ payment }: { payment: Payment }) => {
  const { t } = useSafeTranslation();
  const { toast } = useToast();
  const router = useRouter();

  // Get business rules permissions for this payment
  const permissions = getPaymentPermissions(payment);

  const handleProcess = async () => {
    try {
      await processPayment(payment.id);
      toast({
        title: t('payments.messages.processSuccess'),
        description: t('payments.messages.processSuccessDetail'),
        variant: "default",
      });
      router.refresh();
    } catch (error) {
      toast({
        title: t('payments.messages.processError'),
        variant: "destructive",
      });
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="h-8 w-8 p-0">
          <span className="sr-only">{t('payments.actions.openMenu')}</span>
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>{t('common.actionsHeader')}</DropdownMenuLabel>
        <DropdownMenuSeparator />
        
        {/* View Details - Always allowed */}
        <DropdownMenuItem asChild>
          <Link href={`/payments/${payment.id}`} className="flex items-center">
            <Eye className="mr-2 h-4 w-4" />
            {t('payments.actions.viewDetails')}
          </Link>
        </DropdownMenuItem>

        {/* Edit - Based on business rules */}
        {permissions.canEdit ? (
          <DropdownMenuItem asChild>
            <Link href={`/payments/${payment.id}/edit`} className="flex items-center">
              <Pencil className="mr-2 h-4 w-4" />
              {t('payments.actions.edit')}
              {Object.keys(permissions.editRestrictions).length > 0 && (
                <span className="ml-1 text-xs text-orange-500">*</span>
              )}
            </Link>
          </DropdownMenuItem>
        ) : (
          <DropdownMenuItem disabled>
            <Pencil className="mr-2 h-4 w-4 opacity-50" />
            <span className="opacity-50">{t('payments.actions.edit')}</span>
          </DropdownMenuItem>
        )}

        {/* Process Payment - Only for non-completed payments */}
        {payment.status !== "completed" && (
          <DropdownMenuItem onClick={handleProcess}>
            <CheckCircle className="mr-2 h-4 w-4" />
            {t('payments.actions.process')}
          </DropdownMenuItem>
        )}

        {/* Send SMS Reminder - Only for pending/overdue */}
        {(payment.status === "pending" || payment.status === "overdue") && (
          <PaymentReminderDialog
            payments={[payment]}
            trigger={
              <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                <MessageSquare className="mr-2 h-4 w-4" />
                {t('sms:actions.sendSmsReminder')}
              </DropdownMenuItem>
            }
          />
        )}

        {/* Refund - Only if payment has been paid */}
        {parseFloat(payment.amountPaid || '0') > 0 && (
          <RefundDialog
            payment={payment}
            trigger={
              <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                <RefreshCw className="mr-2 h-4 w-4" />
                {t('payments.refund.title')}
              </DropdownMenuItem>
            }
          />
        )}

        {/* Delete - Based on business rules */}
        <DeletePaymentDialog payment={payment} permissions={permissions} />

        {/* Business Rules Info - Show rationale when edit is restricted */}
        {!permissions.canEdit && (
          <>
            <DropdownMenuSeparator />
            <DropdownMenuItem disabled>
              <div className="text-xs text-muted-foreground">
                {t(permissions.rationale)}
              </div>
            </DropdownMenuItem>
          </>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export const createPaymentColumns = (t: any): ColumnDef<Payment>[] => [
  {
    accessorKey: "athleteId",
    header: () => t('payments.details.athlete'),
    cell: ({ row }) => <AthleteCell payment={row.original} />,
  },
  {
    accessorKey: "amount",
    header: () => t('payments.details.amount'),
    cell: ({ row }) => <AmountCell payment={row.original} />,
  },
  {
    accessorKey: "date",
    header: () => t('payments.details.date'),
    cell: ({ row }) => (
      <div className="space-y-1">
        <DateCell date={row.original.date} label={t('payments.dates.billing')} t={t} />
        <DateCell date={row.original.dueDate} label={t('payments.dates.due')} t={t} />
      </div>
    ),
  },
  {
    accessorKey: "type",
    header: () => t('payments.details.type'),
    cell: ({ row }) => <TypeCell type={row.original.type} />,
  },

  {
    accessorKey: "status",
    header: () => t('payments.details.status'),
    cell: ({ row }) => {
      const payment = row.original;
      if (payment.status === 'partially_paid') {
        const totalAmount = parseFloat(payment.amount);
        const paidAmount = parseFloat(payment.amountPaid || '0');
        const paymentPercentage = totalAmount > 0 ? (paidAmount / totalAmount) * 100 : 0;

        return (
          <div className="flex items-center gap-2">
            <BadgeStatus status={payment.status} type="payment" />
            <span className="text-xs text-muted-foreground font-medium">
              {paymentPercentage.toFixed(0)}%
            </span>
          </div>
        );
      }
      return <BadgeStatus status={payment.status} type="payment" />;
    },
  },
  {
    accessorKey: "description",
    header: () => t('payments.details.description'),
    cell: ({ row }) => <DescriptionCell description={row.getValue("description")} />,
  },
  {
    id: "actions",
    header: () => t('common.actionsHeader'),
    cell: ({ row }) => <ActionsCell payment={row.original} />,
  },
];