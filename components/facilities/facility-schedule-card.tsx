"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Calendar, Clock, Users } from "lucide-react";
import { useSafeTranslation } from "@/hooks/use-safe-translation";

interface FacilitySchedule {
  id: string;
  teamId: string;
  dayOfWeek: number;
  startTime: string;
  endTime: string;
  team: {
    id: string;
    name: string;
  };
}

interface FacilityScheduleCardProps {
  schedules: FacilitySchedule[];
  facilityName: string;
}

// Day names mapping (0 = Sunday, 1 = Monday, etc.)
const dayNames = {
  0: 'sunday',
  1: 'monday', 
  2: 'tuesday',
  3: 'wednesday',
  4: 'thursday',
  5: 'friday',
  6: 'saturday'
} as const;

// Team colors for visual distinction with better contrast and modern look
const teamColors = [
  'bg-blue-50 text-blue-700 border-blue-200 hover:bg-blue-100',
  'bg-green-50 text-green-700 border-green-200 hover:bg-green-100',
  'bg-purple-50 text-purple-700 border-purple-200 hover:bg-purple-100',
  'bg-orange-50 text-orange-700 border-orange-200 hover:bg-orange-100',
  'bg-pink-50 text-pink-700 border-pink-200 hover:bg-pink-100',
  'bg-indigo-50 text-indigo-700 border-indigo-200 hover:bg-indigo-100',
  'bg-yellow-50 text-yellow-700 border-yellow-200 hover:bg-yellow-100',
  'bg-red-50 text-red-700 border-red-200 hover:bg-red-100',
  'bg-teal-50 text-teal-700 border-teal-200 hover:bg-teal-100',
  'bg-cyan-50 text-cyan-700 border-cyan-200 hover:bg-cyan-100',
];

export function FacilityScheduleCard({ schedules, facilityName }: FacilityScheduleCardProps) {
  const { t } = useSafeTranslation();

  // Group schedules by day of week
  const schedulesByDay = schedules.reduce((acc, schedule) => {
    const day = schedule.dayOfWeek;
    if (!acc[day]) {
      acc[day] = [];
    }
    acc[day].push(schedule);
    return acc;
  }, {} as Record<number, FacilitySchedule[]>);

  // Sort schedules within each day by start time
  Object.keys(schedulesByDay).forEach(day => {
    schedulesByDay[parseInt(day)].sort((a, b) => a.startTime.localeCompare(b.startTime));
  });

  // Create a color mapping for teams
  const uniqueTeams = Array.from(new Set(schedules.map(s => s.team.id)));
  const teamColorMap = uniqueTeams.reduce((acc, teamId, index) => {
    acc[teamId] = teamColors[index % teamColors.length];
    return acc;
  }, {} as Record<string, string>);

  // Days of week in order (Monday first)
  const orderedDays = [1, 2, 3, 4, 5, 6, 0]; // Mon, Tue, Wed, Thu, Fri, Sat, Sun

  if (schedules.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            {t('facilities.schedule.title')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-12">
            <div className="mx-auto w-16 h-16 bg-muted/20 rounded-full flex items-center justify-center mb-4">
              <Calendar className="h-8 w-8 text-muted-foreground" />
            </div>
            <h3 className="text-lg font-medium text-muted-foreground mb-2">
              {t('facilities.schedule.noSchedules')}
            </h3>
            <p className="text-sm text-muted-foreground/80">
              {t('facilities.schedule.noSchedulesDescription')}
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Calendar className="h-5 w-5" />
          {t('facilities.schedule.weeklySchedule')}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid gap-4">
          {orderedDays.map(dayOfWeek => {
            const daySchedules = schedulesByDay[dayOfWeek] || [];
            const dayName = dayNames[dayOfWeek as keyof typeof dayNames];
            
            return (
              <div key={dayOfWeek} className="border rounded-lg p-4 bg-gradient-to-r from-muted/20 to-muted/10">
                <div className="flex items-center gap-2 mb-4">
                  <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                    <Clock className="h-4 w-4 text-primary" />
                  </div>
                  <h3 className="font-semibold text-lg text-foreground">
                    {t(`teams.days.${dayName}`)}
                  </h3>
                  {daySchedules.length > 0 && (
                    <Badge variant="secondary" className="ml-auto">
                      {daySchedules.length} {daySchedules.length === 1 ? t('facilities.schedule.session') : t('facilities.schedule.sessions')}
                    </Badge>
                  )}
                </div>
                
                {daySchedules.length === 0 ? (
                  <div className="text-center py-6">
                    <p className="text-sm text-muted-foreground italic">
                      {t('facilities.schedule.noSchedulesDay')}
                    </p>
                  </div>
                ) : (
                  <div className="grid gap-3 sm:grid-cols-2 lg:grid-cols-3">
                    {daySchedules.map((schedule) => (
                      <div
                        key={schedule.id}
                        className={`p-4 rounded-lg border-2 transition-all duration-200 cursor-pointer ${teamColorMap[schedule.team.id]}`}
                      >
                        <div className="flex items-center gap-2 mb-2">
                          <div className="w-6 h-6 bg-current/20 rounded-full flex items-center justify-center">
                            <Clock className="h-3 w-3" />
                          </div>
                          <span className="font-semibold text-sm">
                            {schedule.startTime} - {schedule.endTime}
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="w-6 h-6 bg-current/20 rounded-full flex items-center justify-center">
                            <Users className="h-3 w-3" />
                          </div>
                          <span className="text-sm font-medium truncate">
                            {schedule.team.name}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
}
