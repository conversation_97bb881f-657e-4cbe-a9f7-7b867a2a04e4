"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Badge } from "@/components/ui/badge";
import { Check, ChevronsUpDown, User, CreditCard, PlusCircle } from "lucide-react";
import { cn } from "@/lib/utils";
import { useSafeTranslation } from "@/hooks/use-safe-translation";
import { useToast } from "@/hooks/use-toast";
import { createPaymentTransaction, addAthleteBalance } from "@/lib/actions";

interface AddTransactionDialogProps {
  athletes: any[];
  payments: any[];
  onSuccess?: () => void;
}

export function AddTransactionDialog({ athletes, payments, onSuccess }: AddTransactionDialogProps) {
  const { t } = useSafeTranslation();
  const { toast } = useToast();
  const router = useRouter();

  const [open, setOpen] = useState(false);
  const [selectedAthlete, setSelectedAthlete] = useState<any>(null);
  const [selectedPayment, setSelectedPayment] = useState<any>(null);
  const [transactionType, setTransactionType] = useState<'payment' | 'balance'>('balance');
  const [amount, setAmount] = useState('');
  const [transactionMethod, setTransactionMethod] = useState<'cash' | 'bank_transfer' | 'credit_card'>('cash');
  const [notes, setNotes] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [athleteSearchOpen, setAthleteSearchOpen] = useState(false);
  const [paymentSearchOpen, setPaymentSearchOpen] = useState(false);
  const [athleteSearchQuery, setAthleteSearchQuery] = useState("");

  // Filter payments for selected athlete
  const athletePayments = selectedAthlete 
    ? payments.filter(p => p.athleteId === selectedAthlete.id && ['pending', 'partially_paid', 'overdue'].includes(p.status))
    : [];

  const resetForm = () => {
    setSelectedAthlete(null);
    setSelectedPayment(null);
    setTransactionType('balance');
    setAmount('');
    setTransactionMethod('cash');
    setNotes('');
    setAthleteSearchOpen(false);
    setPaymentSearchOpen(false);
    setAthleteSearchQuery("");
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!selectedAthlete || !amount || !transactionMethod) {
      toast({
        title: t("common.validation.error"),
        description: t("payments.messages.fillRequired"),
        variant: "destructive",
      });
      return;
    }

    if (transactionType === 'payment' && !selectedPayment) {
      toast({
        title: t("common.validation.error"),
        description: t("payments.transactions.selectPayment"),
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);

    try {
      if (transactionType === 'balance') {
        // Add balance to athlete
        await addAthleteBalance(
          selectedAthlete.id,
          amount,
          transactionMethod,
          notes || t('payments.transactions.balanceTopUp')
        );
      } else {
        // Create payment transaction
        await createPaymentTransaction({
          athleteId: selectedAthlete.id,
          paymentId: selectedPayment.id,
          amount,
          transactionMethod,
          notes,
        });
      }

      toast({
        title: t("payments.messages.createSuccess"),
        description: t("payments.transactions.title"),
      });

      setOpen(false);
      resetForm();

      // Refresh the router to get updated data
      router.refresh();

      onSuccess?.();
    } catch (error) {
      console.error("Error creating transaction:", error);
      toast({
        title: t("payments.messages.createError"),
        description: error instanceof Error ? error.message : "Unknown error",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={(newOpen) => {
      setOpen(newOpen);
      if (!newOpen) {
        resetForm();
      }
    }}>
      <DialogTrigger asChild>
        <Button>
          <PlusCircle className="mr-2 h-4 w-4" />
          {t('payments.transactions.new')}
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            {t('payments.transactions.new')}
          </DialogTitle>
          <DialogDescription>
            {t('payments.transactions.createDescription')}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Transaction Type */}
          <div className="space-y-2">
            <Label>{t('payments.transactions.type')}</Label>
            <Select value={transactionType} onValueChange={(value: 'payment' | 'balance') => {
              setTransactionType(value);
              setSelectedPayment(null); // Reset payment selection when changing type
            }}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="balance">{t('payments.transactions.balanceTopUp')}</SelectItem>
                <SelectItem value="payment">{t('payments.transactions.paymentTransaction')}</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Athlete Selection */}
          <div className="space-y-2">
            <Label>{t('payments.transactions.athlete')} *</Label>
            <Popover open={athleteSearchOpen} onOpenChange={setAthleteSearchOpen}>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  role="combobox"
                  aria-expanded={athleteSearchOpen}
                  className="w-full justify-between h-auto min-h-[40px] p-3"
                >
                  {selectedAthlete ? (
                    <div className="flex items-center gap-2 text-left">
                      <User className="h-4 w-4 flex-shrink-0" />
                      <div className="flex flex-col">
                        <span className="font-medium">{selectedAthlete.name} {selectedAthlete.surname}</span>
                        {selectedAthlete.parentPhone && (
                          <span className="text-sm text-muted-foreground">{selectedAthlete.parentPhone}</span>
                        )}
                      </div>
                    </div>
                  ) : (
                    t('payments.transactions.selectAthlete')
                  )}
                  <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-[500px] p-0" align="start">
                <Command>
                  <CommandInput
                    placeholder={t('athletes.placeholders.searchAthletes')}
                    value={athleteSearchQuery}
                    onValueChange={setAthleteSearchQuery}
                  />
                  <CommandList className="max-h-[300px]">
                    {athleteSearchQuery.length < 3 ? (
                      <div className="p-4 text-center text-sm text-muted-foreground">
                        {t('common.search.enterMinChars', { count: 3 })}
                      </div>
                    ) : (
                      <>
                        <CommandEmpty>{t('athletes.messages.noAthletes')}</CommandEmpty>
                        <CommandGroup>
                          {athletes
                            .filter(athlete =>
                              athleteSearchQuery.length >= 3 && (
                                athlete.name.toLowerCase().includes(athleteSearchQuery.toLowerCase()) ||
                                athlete.surname.toLowerCase().includes(athleteSearchQuery.toLowerCase()) ||
                                (athlete.parentPhone && athlete.parentPhone.includes(athleteSearchQuery))
                              )
                            )
                            .map((athlete) => (
                            <CommandItem
                              key={athlete.id}
                              value={`${athlete.name} ${athlete.surname} ${athlete.parentPhone || ''}`}
                              onSelect={() => {
                                setSelectedAthlete(athlete);
                                setSelectedPayment(null); // Reset payment selection
                                setAthleteSearchOpen(false);
                                setAthleteSearchQuery(""); // Reset search query
                              }}
                              className="p-3"
                            >
                              <Check
                                className={cn(
                                  "mr-2 h-4 w-4",
                                  selectedAthlete?.id === athlete.id ? "opacity-100" : "opacity-0"
                                )}
                              />
                              <div className="flex flex-col w-full">
                                <span className="font-medium">{athlete.name} {athlete.surname}</span>
                                {athlete.parentPhone && (
                                  <span className="text-sm text-muted-foreground">{athlete.parentPhone}</span>
                                )}
                                {athlete.parentEmail && (
                                  <span className="text-xs text-muted-foreground">{athlete.parentEmail}</span>
                                )}
                              </div>
                            </CommandItem>
                          ))}
                        </CommandGroup>
                      </>
                    )}
                  </CommandList>
                </Command>
              </PopoverContent>
            </Popover>
          </div>

          {/* Payment Selection (only for payment transactions) */}
          {transactionType === 'payment' && selectedAthlete && (
            <div className="space-y-2">
              <Label>{t('payments.transactions.payment')} *</Label>
              {athletePayments.length === 0 ? (
                <div className="text-sm text-muted-foreground p-3 border rounded-md bg-muted/50">
                  {t('payments.transactions.noOutstandingPayments')}
                </div>
              ) : (
                <Popover open={paymentSearchOpen} onOpenChange={setPaymentSearchOpen}>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      role="combobox"
                      aria-expanded={paymentSearchOpen}
                      className="w-full justify-between h-auto min-h-[40px] p-3"
                    >
                      {selectedPayment ? (
                        <div className="flex items-center gap-2 text-left">
                          <div className="flex flex-col">
                            <div className="flex items-center gap-2">
                              <Badge variant="outline">{t(`payments.status.${selectedPayment.status}`)}</Badge>
                              {selectedPayment.status === 'partially_paid' ? (
                                <div className="flex flex-col">
                                  <span className="font-medium text-xs text-muted-foreground">
                                    {t('payments.details.totalAmount')}: {selectedPayment.amount} {t('common.currency')}
                                  </span>
                                  <span className="font-medium text-orange-600">
                                    {t('payments.remaining')}: {(parseFloat(selectedPayment.amount) - parseFloat(selectedPayment.amountPaid || '0')).toFixed(2)} {t('common.currency')}
                                  </span>
                                </div>
                              ) : (
                                <span className="font-medium">{selectedPayment.amount} {t('common.currency')}</span>
                              )}
                            </div>
                            {selectedPayment.description && (
                              <span className="text-sm text-muted-foreground">{selectedPayment.description}</span>
                            )}
                          </div>
                        </div>
                      ) : (
                        t('payments.transactions.selectPayment')
                      )}
                      <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-[500px] p-0" align="start">
                    <Command>
                      <CommandList className="max-h-[200px]">
                        <CommandEmpty>{t('payments.messages.noPayments')}</CommandEmpty>
                        <CommandGroup>
                          {athletePayments.map((payment) => (
                            <CommandItem
                              key={payment.id}
                              value={payment.description || payment.id}
                              onSelect={() => {
                                setSelectedPayment(payment);
                                setPaymentSearchOpen(false);
                              }}
                              className="p-3"
                            >
                              <Check
                                className={cn(
                                  "mr-2 h-4 w-4",
                                  selectedPayment?.id === payment.id ? "opacity-100" : "opacity-0"
                                )}
                              />
                              <div className="flex flex-col w-full">
                                <div className="flex items-center gap-2">
                                  <Badge variant="outline">{t(`payments.status.${payment.status}`)}</Badge>
                                  {payment.status === 'partially_paid' ? (
                                    <div className="flex flex-col">
                                      <span className="font-medium text-xs text-muted-foreground">
                                        {t('payments.details.totalAmount')}: {payment.amount} {t('common.currency')}
                                      </span>
                                      <span className="font-medium text-orange-600">
                                        {t('payments.remaining')}: {(parseFloat(payment.amount) - parseFloat(payment.amountPaid || '0')).toFixed(2)} {t('common.currency')}
                                      </span>
                                    </div>
                                  ) : (
                                    <span className="font-medium">{payment.amount} {t('common.currency')}</span>
                                  )}
                                </div>
                                {payment.description && (
                                  <span className="text-sm text-muted-foreground">{payment.description}</span>
                                )}
                                <span className="text-xs text-muted-foreground">
                                  {t('payments.details.dueDate')}: {payment.dueDate}
                                </span>
                              </div>
                            </CommandItem>
                          ))}
                        </CommandGroup>
                      </CommandList>
                    </Command>
                  </PopoverContent>
                </Popover>
              )}
            </div>
          )}

          {/* Amount */}
          <div className="space-y-2">
            <Label htmlFor="amount">{t('payments.transactions.amount')} *</Label>
            <Input
              id="amount"
              type="number"
              step="0.01"
              min="0"
              value={amount}
              onChange={(e) => setAmount(e.target.value)}
              placeholder="0.00"
            />
          </div>

          {/* Transaction Method */}
          <div className="space-y-2">
            <Label>{t('payments.transactions.method')} *</Label>
            <Select value={transactionMethod} onValueChange={(value: 'cash' | 'bank_transfer' | 'credit_card') => setTransactionMethod(value)}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="cash">{t('payments.transactionMethods.cash')}</SelectItem>
                <SelectItem value="bank_transfer">{t('payments.transactionMethods.bank_transfer')}</SelectItem>
                <SelectItem value="credit_card">{t('payments.transactionMethods.credit_card')}</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Notes */}
          <div className="space-y-2">
            <Label htmlFor="notes">{t('payments.transactions.notes.label')}</Label>
            <Textarea
              id="notes"
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              placeholder={t('payments.transactions.notesPlaceholder')}
              rows={3}
            />
          </div>
        </form>

        <DialogFooter>
          <Button type="button" variant="outline" onClick={() => setOpen(false)}>
            {t('common.actions.cancel')}
          </Button>
          <Button type="submit" onClick={handleSubmit} disabled={isSubmitting}>
            {isSubmitting ? t('common.actions.creating') : t('common.actions.create')}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
