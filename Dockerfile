# Multi-stage Dockerfile for Next.js with pnpm
# Stage 1: Base image with Node.js and pnpm
FROM node:22-alpine3.20 AS base

# Install necessary packages, timezone data, and pnpm
RUN apk add --no-cache libc6-compat dumb-init tzdata
ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"
RUN corepack enable

# Stage 2: Dependencies installation
FROM base AS deps

# Set working directory
WORKDIR /app

# Copy package files
COPY package.json pnpm-lock.yaml pnpm-workspace.yaml ./

# Install dependencies
RUN --mount=type=cache,id=pnpm,target=/pnpm/store pnpm install --frozen-lockfile

# Stage 3: Build the application
FROM base AS builder

# Set working directory
WORKDIR /app

# Copy dependencies from deps stage
COPY --from=deps /app/node_modules ./node_modules

# Copy source code
COPY . .

# Accept only non-sensitive build arguments
ARG DATABASE_URL

# Set environment for build (only non-sensitive data)
ENV NEXT_TELEMETRY_DISABLED=1
ENV NODE_ENV=production
ENV DATABASE_URL=${DATABASE_URL}

# Build the application
RUN pnpm run build

# Stage 4: Production runtime
FROM node:22-alpine3.20 AS runner

# Install timezone data in runtime stage as well
RUN apk add --no-cache tzdata

# Create app user for security
RUN addgroup --gid 1001 --system nodejs && \
    adduser --system --uid 1001 nextjs

# Set working directory
WORKDIR /app

# Copy built application from builder stage
COPY --from=builder --chown=nextjs:nodejs /app/public ./public

# Copy the built Next.js application
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

# Copy database migrations and scripts if needed
COPY --from=builder --chown=nextjs:nodejs /app/drizzle ./drizzle
COPY --from=builder --chown=nextjs:nodejs /app/scripts ./scripts

# Copy locales for i18n
COPY --from=builder --chown=nextjs:nodejs /app/public/locales ./public/locales

# Set environment variables
ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1
ENV PORT=3000
ENV HOSTNAME="0.0.0.0"
ENV TZ=UTC

# Switch to non-root user
USER nextjs

# Expose port
EXPOSE 3000

# Health check using node instead of curl
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD node -e "http.get('http://localhost:3000/api/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) }).on('error', () => process.exit(1))"

# Start the application (Alpine has built-in signal handling)
CMD ["node", "server.js"]
