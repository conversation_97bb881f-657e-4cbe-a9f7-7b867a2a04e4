import { NextRequest, NextResponse } from 'next/server';
import { getToken } from 'next-auth/jwt';
import logger from '@/lib/logger';

export async function GET(request: NextRequest) {
  try {
    const token = await getToken({ 
      req: request, 
      secret: process.env.NEXTAUTH_SECRET,
      secureCookie: process.env.NODE_ENV === 'production'
    });
    
    if (!token) {
      return NextResponse.json({ error: 'No valid token' }, { status: 401 });
    }

    const response = NextResponse.json({ success: true });
    
    // Set httpOnly, secure cookies for tokens
    const cookieOptions = {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax' as const,
      path: '/',
      maxAge: 60 * 60 * 24 * 7, // 7 days
    };

    if (token.accessToken) {
      response.cookies.set('access-token', token.accessToken as string, cookieOptions);
    }
    
    if (token.idToken) {
      response.cookies.set('id-token', token.idToken as string, cookieOptions);
    }
    
    if (token.refreshToken) {
      response.cookies.set('refresh-token', token.refreshToken as string, cookieOptions);
    }

    return response;
  } catch (error) {
    logger.error('Error setting token cookies:', { error });
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
