import { NextRequest, NextResponse } from 'next/server';
import { fetchEntityName } from '@/lib/entity-name-fetcher';
import logger from '@/lib/logger';

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const entityType = searchParams.get('type');
    const entityId = searchParams.get('id');

    if (!entityType || !entityId) {
      return NextResponse.json(
        { error: 'Missing entityType or entityId parameter' },
        { status: 400 }
      );
    }

    const entityName = await fetchEntityName(entityType, entityId);
    
    return NextResponse.json({ 
      entityName,
      entityType,
      entityId 
    });
  } catch (error) {
    logger.error('Entity name API error:', { error });
    return NextResponse.json(
      { error: 'Failed to fetch entity name' },
      { status: 500 }
    );
  }
}
