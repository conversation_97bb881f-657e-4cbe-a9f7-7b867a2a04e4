import { Suspense } from "react";
import { getPaymentsPaginated, getPaymentPlans, getPaymentTransactionsPaginated, getAthletes } from "@/lib/actions";
import { PaymentsListPaginated } from "./payments-list-paginated";

interface PaymentsPageProps {
  searchParams: Promise<{
    page?: string;
    limit?: string;
    search?: string;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
    tab?: string;
    status?: string;
    type?: string;
    fromDate?: string;
    toDate?: string;
    transactionMethod?: string;
    athleteId?: string;
    paymentId?: string;
  }>;
}

function PaymentsListSkeleton() {
  return (
    <div className="space-y-4">
      {[1, 2, 3, 4, 5].map((i) => (
        <div key={i} className="h-16 bg-muted animate-pulse rounded-md" />
      ))}
    </div>
  );
}

async function PaymentsListServer({ searchParams }: PaymentsPageProps) {
  const resolvedSearchParams = await searchParams;
  const page = parseInt(resolvedSearchParams.page || '1');
  const limit = parseInt(resolvedSearchParams.limit || '10');
  const search = resolvedSearchParams.search;
  const sortBy = resolvedSearchParams.sortBy || 'date';
  const sortOrder = resolvedSearchParams.sortOrder || 'desc';
  
  const filters: Record<string, string> = {};
  if (resolvedSearchParams.status && resolvedSearchParams.status !== 'all') filters.status = resolvedSearchParams.status;
  if (resolvedSearchParams.type && resolvedSearchParams.type !== 'all') filters.type = resolvedSearchParams.type;
  if (resolvedSearchParams.fromDate) filters.fromDate = resolvedSearchParams.fromDate;
  if (resolvedSearchParams.toDate) filters.toDate = resolvedSearchParams.toDate;

  // Transaction filters
  const transactionFilters: Record<string, string> = {};
  if (resolvedSearchParams.transactionMethod && resolvedSearchParams.transactionMethod !== 'all') transactionFilters.transactionMethod = resolvedSearchParams.transactionMethod;
  if (resolvedSearchParams.athleteId) transactionFilters.athleteId = resolvedSearchParams.athleteId;
  if (resolvedSearchParams.paymentId) transactionFilters.paymentId = resolvedSearchParams.paymentId;
  if (resolvedSearchParams.fromDate) transactionFilters.fromDate = resolvedSearchParams.fromDate;
  if (resolvedSearchParams.toDate) transactionFilters.toDate = resolvedSearchParams.toDate;

  // Fetch data based on active tab
  const isTransactionsTab = resolvedSearchParams.tab === 'transactions';

  const promises = [
    getPaymentsPaginated(page, limit, search, sortBy, sortOrder, filters),
    getPaymentPlans()
  ];

  // Only fetch transaction data and related data if transactions tab is active
  if (isTransactionsTab) {
    promises.push(
      getPaymentTransactionsPaginated(
        page,
        limit,
        search,
        sortBy || 'transactionDate',
        sortOrder,
        transactionFilters
      ),
      getAthletes(), // For athlete selection in transaction dialog
      getPaymentsPaginated(1, 1000, undefined, 'dueDate', 'asc', {}) // Get all payments for selection
    );
  }

  const results = await Promise.all(promises);
  const [paymentsResult, plans, transactionsResult, athletes, allPayments] = results;


  // Create a serialized version of the search params to avoid _debugInfo issues
  const serializedSearchParams = {
    page: resolvedSearchParams.page,
    limit: resolvedSearchParams.limit,
    search: resolvedSearchParams.search,
    sortBy: resolvedSearchParams.sortBy,
    sortOrder: resolvedSearchParams.sortOrder,
    tab: resolvedSearchParams.tab,
    status: resolvedSearchParams.status,
    type: resolvedSearchParams.type,
    fromDate: resolvedSearchParams.fromDate,
    toDate: resolvedSearchParams.toDate,
    transactionMethod: resolvedSearchParams.transactionMethod,
    athleteId: resolvedSearchParams.athleteId,
    paymentId: resolvedSearchParams.paymentId,
  };

  return (
    <PaymentsListPaginated
      initialData={paymentsResult.data || []}
      initialPagination={paymentsResult.pagination || {
        page: 1,
        limit: 10,
        total: 0,
        totalPages: 0,
        hasNextPage: false,
        hasPreviousPage: false,
      }}
      plans={plans}
      transactionData={transactionsResult?.data}
      transactionPagination={transactionsResult?.pagination}
      athletes={athletes || []}
      allPayments={allPayments?.data || []}
      initialSearchParams={serializedSearchParams}
    />
  );
}

export default function PaymentsPage({ searchParams }: PaymentsPageProps) {
  return (
    <Suspense fallback={<PaymentsListSkeleton />}>
      <PaymentsListServer searchParams={searchParams} />
    </Suspense>
  );
}