"use client";

import { useRouter } from "next/navigation";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ArrowLeft, Pencil, Users, Calendar, Clock, Building2, Trash2 } from "lucide-react";
import { TurkishLiraIcon } from "@/components/ui/turkish-lira-icon";
import Link from "next/link";
import { PaymentPlan } from "@/lib/types";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { useSafeTranslation } from "@/hooks/use-safe-translation";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { useState } from "react";
import { deletePaymentPlan } from "@/lib/actions";
import {useToast} from "@/hooks/use-toast";

interface PaymentPlanDetailsClientProps {
  paymentPlan: PaymentPlan;
  assignedAthletesCount?: number;
}

export default function PaymentPlanDetailsClient({ paymentPlan, assignedAthletesCount = 0 }: PaymentPlanDetailsClientProps) {
  const router = useRouter();
  const { t } = useSafeTranslation();
  const { toast } = useToast();
  const [isDeleting, setIsDeleting] = useState(false);

  // Format amount for display
  const formattedAmount = new Intl.NumberFormat('tr-TR', {
    style: 'currency',
    currency: 'TRY'
  }).format(parseFloat(paymentPlan.monthlyValue || '0'));

  // Get translated status
  const getStatusTranslation = (status: string) => {
    return t(`payments.planEdit.status.${status}`, status);
  };

  // Helper function to get ordinal suffix using i18n
  const getOrdinalSuffix = (day: number) => {
    if (day >= 11 && day <= 13) {
      return t('payments.planDetail.schedule.ordinalTh');
    }
    
    const lastDigit = day % 10;
    switch (lastDigit) {
      case 1:
        return t('payments.planDetail.schedule.ordinalSt');
      case 2:
        return t('payments.planDetail.schedule.ordinalNd');
      case 3:
        return t('payments.planDetail.schedule.ordinalRd');
      default:
        return t('payments.planDetail.schedule.ordinalTh');
    }
  };

  // Helper function to format day with ordinal using i18n
  const formatDayWithOrdinal = (day: number) => {
    return `${day}${getOrdinalSuffix(day)}`;
  };

  // Calculate grace period
  const gracePeriod = paymentPlan.dueDay - paymentPlan.assignDay;

  // Helper function to format date with translated month names
  const formatDateWithTranslation = (date: Date) => {
    const monthNames = [
      'january', 'february', 'march', 'april', 'may', 'june',
      'july', 'august', 'september', 'october', 'november', 'december'
    ];
    
    const month = monthNames[date.getMonth()];
    const day = date.getDate();
    const year = date.getFullYear();
    
    return `${t(`common.months.${month}`, { ns: 'shared' })} ${day}, ${year}`;
  };

  const handleDelete = async () => {
    setIsDeleting(true);
    try {
      const result = await deletePaymentPlan(paymentPlan.id);
      
      if (result.success) {
        toast({
          title: t('common.success'),
          description: t('payments.planDetail.messages.deleteSuccess')
        });
        router.push('/payments?tab=plans')
      } else {
        let errorDescriptionKey = '';
        if(result.errorType == 'BusinessRuleError'){
          errorDescriptionKey = `errors.${result.error}`;
        }else{
          errorDescriptionKey = 'payments.planDetail.messages.deleteError';
        }
        toast({
          title: t('common.error'),
          description: t(errorDescriptionKey),
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error deleting payment plan:', error);
      toast({
        title: t('common.error'),
        description: t('payments.planDetail.messages.deleteError'),
        variant: 'destructive',
      });
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" onClick={() => router.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            {t('common.actions.back')}
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">{paymentPlan.name}</h1>
            <p className="text-muted-foreground">
              {t('payments.planDetail.subtitle', { 
                date: formatDateWithTranslation(new Date(paymentPlan.createdAt))
              })}
            </p>
          </div>
        </div>
        <div className="flex gap-2">
          <Button asChild>
            <Link href={`/payments/plans/${paymentPlan.id}/edit`}>
              <Pencil className="mr-2 h-4 w-4" />
              {t('payments.planDetail.editPlan')}
            </Link>
          </Button>
          
          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button variant="destructive">
                <Trash2 className="mr-2 h-4 w-4" />
                {t('payments.planDetail.actions.delete')}
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>
                  {t('payments.planDetail.deleteDialog.title')}
                </AlertDialogTitle>
                <AlertDialogDescription>
                  {assignedAthletesCount > 0 ? (
                    t('payments.planDetail.deleteDialog.descriptionWithAssignments', { 
                      count: assignedAthletesCount 
                    })
                  ) : (
                    t('payments.planDetail.deleteDialog.description')
                  )}
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>
                  {t('common.actions.cancel')}
                </AlertDialogCancel>
                <AlertDialogAction
                  onClick={handleDelete}
                  disabled={isDeleting}
                  className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                >
                  {isDeleting ? t('common.actions.deleting') : t('common.actions.delete')}
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </div>
      </div>

      <div className="grid gap-6 lg:grid-cols-3">
        {/* Main Details Card */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TurkishLiraIcon className="h-5 w-5" />
                {t('payments.planDetail.overview.title')}
              </CardTitle>
              <div className="text-sm text-muted-foreground flex items-center gap-2">
                <span>{t('payments.planDetail.details.status')}:</span>
                <Badge variant={paymentPlan.status === 'active' ? 'default' : 'secondary'}>
                  {getStatusTranslation(paymentPlan.status)}
                </Badge>
              </div>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Amount & Schedule */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="p-6 rounded-lg bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 border border-blue-200 dark:border-blue-800">
                  <div className="flex items-center gap-2 mb-2">
                    <p className="text-sm font-medium text-blue-900 dark:text-blue-100">{t('payments.planDetail.overview.paymentAmount')}</p>
                  </div>
                  <p className="text-3xl font-bold text-blue-900 dark:text-blue-100">{formattedAmount}</p>
                  <p className="text-sm text-blue-700 dark:text-blue-300 mt-1">
                    {t('payments.planDetail.schedule.monthlyInterval')}
                  </p>
                </div>
                
                <div className="p-6 rounded-lg bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 border border-green-200 dark:border-green-800">
                  <div className="flex items-center gap-2 mb-2">
                    <Calendar className="h-5 w-5 text-green-600" />
                    <p className="text-sm font-medium text-green-900 dark:text-green-100">{t('payments.planDetail.schedule.title')}</p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-lg font-semibold text-green-900 dark:text-green-100">
                      {t('payments.planDetail.schedule.assignedOn', { 
                        day: paymentPlan.assignDay
                      })}
                    </p>
                    <p className="text-sm text-green-700 dark:text-green-300">
                      {t('payments.planDetail.schedule.dueOn', { 
                        day: paymentPlan.dueDay
                      })}
                    </p>
                    {gracePeriod > 0 && (
                      <p className="text-xs text-green-600 dark:text-green-400 mt-2">
                        {t('payments.planDetail.schedule.gracePeriod', { days: gracePeriod })}
                      </p>
                    )}
                  </div>
                </div>
              </div>

              {/* Description */}
              {paymentPlan.description && (
                <>
                  <Separator />
                  <div>
                    <h3 className="text-lg font-semibold mb-2">{t('payments.planDetail.overview.description')}</h3>
                    <p className="text-muted-foreground leading-relaxed">
                      {paymentPlan.description}
                    </p>
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Quick Stats */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="h-5 w-5" />
                {t('payments.planDetail.stats.title')}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">{t('payments.planDetail.stats.assignDay')}</span>
                <span className="font-medium">{formatDayWithOrdinal(paymentPlan.assignDay)} {t('payments.planDetail.schedule.monthlyInterval').toLowerCase()}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">{t('payments.planDetail.stats.dueDay')}</span>
                <span className="font-medium">{formatDayWithOrdinal(paymentPlan.dueDay)} {t('payments.planDetail.schedule.monthlyInterval').toLowerCase()}</span>
              </div>
              {gracePeriod > 0 && (
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">{t('payments.planDetail.stats.gracePeriod')}</span>
                  <span className="font-medium">{gracePeriod} {gracePeriod === 1 ? 'day' : 'days'}</span>
                </div>
              )}
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">{t('payments.planDetail.details.status')}</span>
                <Badge variant="outline" className="capitalize">
                  {getStatusTranslation(paymentPlan.status)}
                </Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">{t('payments.planDetail.stats.branches')}</span>
                <span className="font-medium">{paymentPlan.branches?.length || 0}</span>
              </div>
            </CardContent>
          </Card>

          {/* Available Branches */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building2 className="h-5 w-5" />
                {t('payments.planDetail.branches.title')}
              </CardTitle>
              <CardDescription>
                {t('payments.planDetail.branches.description', { 
                  count: paymentPlan.branches?.length || 0 
                })}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {paymentPlan.branches?.length ? (
                  paymentPlan.branches.map((branch) => (
                    <div key={branch.id} className="flex items-center justify-between p-3 rounded-lg bg-muted/50">
                      <div>
                        <p className="font-medium">{t(`common.branches.${branch.name}`, { ns: 'shared' })}</p>
                        {branch.description && (
                          <p className="text-sm text-muted-foreground">{branch.description}</p>
                        )}
                      </div>
                      <Badge variant="secondary">{t('payments.planDetail.status.active')}</Badge>
                    </div>
                  ))
                ) : (
                  <p className="text-sm text-muted-foreground">{t('payments.planDetail.branches.noBranches')}</p>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
