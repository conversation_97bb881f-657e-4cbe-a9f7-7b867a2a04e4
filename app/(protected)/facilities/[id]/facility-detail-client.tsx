"use client";

import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { ArrowLeft, Edit, MapPin, Users, Ruler, Building } from "lucide-react";
import Link from "next/link";
import type { Facility } from "@/lib/types";
import { useSafeTranslation } from "@/hooks/use-safe-translation";
import { FacilityScheduleCard } from "@/components/facilities/facility-schedule-card";

interface FacilitySchedule {
  id: string;
  teamId: string;
  dayOfWeek: number;
  startTime: string;
  endTime: string;
  team: {
    id: string;
    name: string;
  };
}

interface FacilityDetailClientProps {
  facility: Facility;
  schedules: FacilitySchedule[];
}

export default function FacilityDetailClient({ facility, schedules }: FacilityDetailClientProps) {
  const { t } = useSafeTranslation();

  const formatDimensions = () => {
    // First try the computed capacity.dimensions structure
    if (facility.capacity?.dimensions?.length && facility.capacity?.dimensions?.width) {
      const unit = facility.capacity.dimensions.unit || 'meters';
      return `${facility.capacity.dimensions.length} × ${facility.capacity.dimensions.width} ${t(`facilities.units.${unit}`)}`;
    } 
    
    // Fallback to direct properties if capacity.dimensions is not available
    if (facility.length && facility.width && facility.dimensionUnit) {
      return `${facility.length} × ${facility.width} ${t(`facilities.units.${facility.dimensionUnit}`)}`;
    }
    
    return t('common.notAssigned');
  };

  const getCapacityDisplay = () => {
    // First try the computed capacity.total
    const totalCapacity = facility.capacity?.total || facility.totalCapacity;
    
    if (totalCapacity && totalCapacity > 0) {
      return `${totalCapacity} ${t('facilities.details.people')}`;
    }
    
    return t('common.notAssigned');
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="outline" size="sm" asChild>
            <Link href="/facilities">
              <ArrowLeft className="h-4 w-4 mr-2" />
              {t('common.actions.back')}
            </Link>
          </Button>
          <h1 className="text-2xl font-bold">{t('facilities.details.information')}</h1>
        </div>
        <Button asChild>
          <Link href={`/facilities/${facility.id}/edit`}>
            <Edit className="h-4 w-4 mr-2" />
            {t('facilities.actions.edit')}
          </Link>
        </Button>
      </div>

      {/* Facility Details Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Building className="h-5 w-5" />
            <span>{facility.name}</span>
          </CardTitle>
          <CardDescription>
            {t('facilities.details.type')}: {t(`facilities.types.${facility.type}`)}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Type and Capacity */}
            <div className="space-y-4">
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Building className="h-4 w-4 text-muted-foreground" />
                  <label className="text-sm font-medium">{t('facilities.details.type')}</label>
                </div>
                <Badge variant="secondary" className="capitalize">
                  {t(`facilities.types.${facility.type}`)}
                </Badge>
              </div>

              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Users className="h-4 w-4 text-muted-foreground" />
                  <label className="text-sm font-medium">{t('facilities.details.capacity')}</label>
                </div>
                <p className="text-lg font-semibold text-primary">
                  {getCapacityDisplay()}
                </p>
              </div>

              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Ruler className="h-4 w-4 text-muted-foreground" />
                  <label className="text-sm font-medium">{t('facilities.details.dimensions')}</label>
                </div>
                <p className="text-sm font-medium">{formatDimensions()}</p>
              </div>
            </div>

            {/* Address */}
            <div className="space-y-4">
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <MapPin className="h-4 w-4 text-muted-foreground" />
                  <label className="text-sm font-medium">{t('facilities.details.address')}</label>
                </div>
                <p className="text-sm bg-muted/50 p-3 rounded-md">{facility.address}</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Facility Schedule Card */}
      <FacilityScheduleCard
        schedules={schedules}
        facilityName={facility.name}
      />
    </div>
  );
}
