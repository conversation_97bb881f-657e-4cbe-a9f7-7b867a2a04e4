'use client';

import { Suspense, useEffect, useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { MessageSquare, Settings, BarChart3, CreditCard, AlertTriangle } from 'lucide-react';
import Link from 'next/link';
import { getSmsBalance, getBalanceStatus, getSmsStats } from '@/lib/actions/sms';
import { useSafeTranslation } from '@/hooks/use-safe-translation';
import QuickSmsActions from '@/components/sms/quick-sms-actions';
import SmsBalanceDisplay from '@/components/sms/sms-balance-display';
import { useSms } from '@/contexts/sms-context';

function SmsOverview() {
  const { t } = useSafeTranslation();
  const { dataRefreshTrigger } = useSms();
  const [data, setData] = useState<{
    stats: any;
  } | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function loadData() {
      try {
        const stats = await getSmsStats();
        setData({ stats });
      } catch (err) {
        setError('Error loading SMS data');
      } finally {
        setLoading(false);
      }
    }

    loadData();
  }, [dataRefreshTrigger]); // Refresh when SMS operations complete

  if (loading) {
    return <SmsOverviewSkeleton />;
  }

  if (error || !data) {
    return (
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardContent className="p-6">
            <p className="text-sm text-muted-foreground">{error || 'Error loading SMS data'}</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  const { stats } = data;

  return (
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
      {/* Balance Card - Reactive */}
      <SmsBalanceDisplay />

      {/* Total SMS Sent */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">{t('sms:stats.totalSent')}</CardTitle>
          <MessageSquare className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
            {stats?.reduce((total: number, stat: any) => total + (stat.total || 0), 0) || 0}
          </div>
          <p className="text-xs text-muted-foreground">
            {t('sms:stats.allTime')}
          </p>
        </CardContent>
      </Card>

      {/* Payment Reminders */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">{t('sms:stats.paymentReminders')}</CardTitle>
          <BarChart3 className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
            {stats?.filter((stat: any) => stat.type === 'payment_reminder')
              .reduce((total: number, stat: any) => total + (stat.total || 0), 0) || 0}
          </div>
          <p className="text-xs text-muted-foreground">
            {t('sms:stats.paymentReminderSms')}
          </p>
        </CardContent>
      </Card>

      {/* Team Messages */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">{t('sms:stats.teamMessages')}</CardTitle>
          <MessageSquare className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
            {stats?.filter((stat: any) => stat.type === 'team_message')
              .reduce((total: number, stat: any) => total + (stat.total || 0), 0) || 0}
          </div>
          <p className="text-xs text-muted-foreground">
            {t('sms:stats.teamMessagesSent')}
          </p>
        </CardContent>
      </Card>
    </div>
  );
}

function SmsOverviewSkeleton() {
  return (
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
      {[...Array(4)].map((_, i) => (
        <Card key={i}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <div className="h-4 w-24 bg-muted animate-pulse rounded" />
            <div className="h-4 w-4 bg-muted animate-pulse rounded" />
          </CardHeader>
          <CardContent>
            <div className="h-8 w-16 bg-muted animate-pulse rounded mb-2" />
            <div className="h-4 w-20 bg-muted animate-pulse rounded" />
          </CardContent>
        </Card>
      ))}
    </div>
  );
}

export default function SmsPageClient() {
  const { t } = useSafeTranslation();
  
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{t('sms:title')}</h1>
          <p className="text-muted-foreground">
            {t('sms:description')}
          </p>
        </div>
      </div>

      <Suspense fallback={<SmsOverviewSkeleton />}>
        <SmsOverview />
      </Suspense>

      {/* Quick SMS Actions */}
      <QuickSmsActions />

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {/* Configuration Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Settings className="h-5 w-5" />
              <span>{t('sms:cards.configuration.title')}</span>
            </CardTitle>
            <CardDescription>
              {t('sms:cards.configuration.description')}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <p className="text-sm text-muted-foreground">
                {t('sms:cards.configuration.content')}
              </p>
              <Button asChild className="w-full">
                <Link href="/sms/configuration">
                  {t('sms:cards.configuration.action')}
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Send SMS Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <MessageSquare className="h-5 w-5" />
              <span>{t('sms:cards.sendSms.title')}</span>
            </CardTitle>
            <CardDescription>
              {t('sms:cards.sendSms.description')}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <p className="text-sm text-muted-foreground">
                {t('sms:cards.sendSms.content')}
              </p>
              <div className="space-y-2">
                <Button asChild className="w-full" variant="outline">
                  <Link href="/payments">
                    {t('sms:cards.sendSms.paymentReminders')}
                  </Link>
                </Button>
                <Button asChild className="w-full" variant="outline">
                  <Link href="/teams">
                    {t('sms:cards.sendSms.teamMessages')}
                  </Link>
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Balance Management Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <CreditCard className="h-5 w-5" />
              <span>{t('sms:cards.balance.title')}</span>
            </CardTitle>
            <CardDescription>
              {t('sms:cards.balance.description')}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <p className="text-sm text-muted-foreground">
                {t('sms:cards.balance.content')}
              </p>
              <div className="space-y-1">
                <Button asChild className="w-full">
                  <Link href="/sms/balance">
                    {t('sms:cards.balance.manageBalance')}
                  </Link>
                </Button>
                <p className="text-xs text-amber-600">
                  ⚠️ {t('sms:balance.management.disabled')}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>{t('sms:quickActions.title')}</CardTitle>
          <CardDescription>
            {t('sms:quickActions.description')}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            <Button asChild variant="outline" className="h-auto p-4">
              <Link href="/sms/balance" className="flex flex-col items-center space-y-2">
                <CreditCard className="h-6 w-6" />
                <span>{t('sms:quickActions.addCredits')}</span>
              </Link>
            </Button>
            <Button asChild variant="outline" className="h-auto p-4">
              <Link href="/payments?status=pending" className="flex flex-col items-center space-y-2">
                <MessageSquare className="h-6 w-6" />
                <span>{t('sms:quickActions.pendingReminders')}</span>
              </Link>
            </Button>
            <Button asChild variant="outline" className="h-auto p-4">
              <Link href="/payments?status=overdue" className="flex flex-col items-center space-y-2">
                <MessageSquare className="h-6 w-6" />
                <span>{t('sms:quickActions.overdueReminders')}</span>
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
