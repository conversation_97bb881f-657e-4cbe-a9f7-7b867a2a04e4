import { useState } from "react";
import { getTodayAsLocalString } from "@/lib/utils/date-formatter";
import { useSafeTranslation } from "@/hooks/use-safe-translation";

export interface AthleteFormData {
  name: string;
  surname: string;
  nationalId: string;
  birthDate: string;
  registrationDate: string;
  parent: {
    name: string;
    surname: string;
    phone: string;
    email: string;
    address: string;
  };
}

interface Athlete {
  id: string;
  name: string;
  surname: string;
  nationalId: string;
  birthDate: string;
  registrationDate: string;
  parentName: string | null;
  parentSurname: string | null;
  parentPhone: string | null;
  parentEmail: string | null;
  parentAddress: string | null;
}

interface UseAthleteFormProps {
  athlete: Athlete;
}

export function useAthleteForm({ athlete }: UseAthleteFormProps) {
  const { t } = useSafeTranslation();
  
  const [formData, setFormData] = useState<AthleteFormData>({
    name: athlete.name,
    surname: athlete.surname,
    nationalId: athlete.nationalId,
    birthDate: athlete.birthDate.split('T')[0], // Ensure format is YYYY-MM-DD
    registrationDate: athlete.registrationDate?.split('T')[0] || getTodayAsLocalString(), // Ensure format is YYYY-MM-DD
    parent: {
      name: athlete.parentName || "",
      surname: athlete.parentSurname || "",
      phone: athlete.parentPhone || "",
      email: athlete.parentEmail || "",
      address: athlete.parentAddress || "",
    },
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  const updateField = (field: string, value: string) => {
    if (field.startsWith('parent.')) {
      const parentField = field.replace('parent.', '');
      setFormData(prev => ({
        ...prev,
        parent: {
          ...prev.parent,
          [parentField]: value,
        },
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [field]: value,
      }));
    }

    // Clear error when field is updated
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: "" }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = t('common.validation.nameRequired');
    }
    if (!formData.surname.trim()) {
      newErrors.surname = t('common.validation.surnameRequired');
    }
    if (!formData.nationalId.trim()) {
      newErrors.nationalId = t('common.validation.nationalIdRequired');
    }
    if (!formData.birthDate) {
      newErrors.birthDate = t('common.validation.birthDateRequired');
    }
    if (!formData.registrationDate) {
      newErrors.registrationDate = t('common.validation.registrationDateRequired');
    }

    // Validate birth date is not in the future
    if (formData.birthDate && new Date(formData.birthDate) > new Date()) {
      newErrors.birthDate = t('common.validation.birthDateFuture');
    }

    // Validate registration date is not in the future
    if (formData.registrationDate && new Date(formData.registrationDate) > new Date()) {
      newErrors.registrationDate = t('common.validation.registrationDateFuture');
    }

    // Email validation if provided
    if (formData.parent.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.parent.email)) {
      newErrors['parent.email'] = t('common.validation.email');
    }

    // Parent phone validation
    if (formData.parent.phone) {
      const cleanedPhone = formData.parent.phone.replace(/[\s\-]/g, '').replace(/\+90/g, '').replace(/^0/, '');
      const parentPhoneRegex = /^[1-9]\d{9}$/;
      if (!parentPhoneRegex.test(cleanedPhone)) {
        newErrors['parent.phone'] = t('common.validation.parentPhone');
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const resetForm = () => {
    setFormData({
      name: athlete.name,
      surname: athlete.surname,
      nationalId: athlete.nationalId,
      birthDate: athlete.birthDate.split('T')[0],
      registrationDate: athlete.registrationDate?.split('T')[0] || new Date().toISOString().split('T')[0],
      parent: {
        name: athlete.parentName || "",
        surname: athlete.parentSurname || "",
        phone: athlete.parentPhone || "",
        email: athlete.parentEmail || "",
        address: athlete.parentAddress || "",
      },
    });
    setErrors({});
  };

  return {
    formData,
    errors,
    updateField,
    validateForm,
    resetForm,
    setFormData,
  };
}
