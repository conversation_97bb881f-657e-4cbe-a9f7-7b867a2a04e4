import { useState } from "react";
import { useRouter } from "next/navigation";
import { useToast } from "@/hooks/use-toast";
import { useSafeTranslation } from "@/hooks/use-safe-translation";
import { updatePayment } from "@/lib/actions/payments";
import type { PaymentFormData } from "./usePaymentForm";

interface UsePaymentOperationsProps {
  paymentId: string;
}

export function usePaymentOperations({ paymentId }: UsePaymentOperationsProps) {
  const router = useRouter();
  const { toast } = useToast();
  const { t } = useSafeTranslation();
  const [isSaving, setIsSaving] = useState(false);

  const savePayment = async (formData: PaymentFormData): Promise<boolean> => {
    setIsSaving(true);
    try {
      const result = await updatePayment(paymentId, {
        athleteId: formData.athleteId,
        amount: formData.amount,
        date: formData.date,
        dueDate: formData.dueDate,
        status: formData.status,
        type: formData.type,
        method: formData.method === "" ? null : formData.method as "cash" | "bank_transfer" | "credit_card",
        description: formData.description || null,
      });

      if (result.success) {
        toast({
          title: t("payments.messages.updateSuccess"),
          description: t("payments.messages.updateSuccessDescription"),
        });
        return true;
      } else {
        console.error("Error updating payment:", result.error);
        let errorDescriptionKey = '';
        if(result.errorType == 'BusinessRuleError'){
          errorDescriptionKey = `errors.${result.error}`;
        }else{
          errorDescriptionKey = 'payments.messages.updateErrorDescription';
        }
        
        toast({
          title: t("payments.messages.updateError"),
          description: t(errorDescriptionKey),
          variant: "destructive",
        });
        return false;
      }
    } catch (error) {
      console.error("Error updating payment:", error);
      toast({
        title: t("payments.messages.updateError"),
        description: error instanceof Error ? error.message : t("payments.messages.updateErrorDescription"),
        variant: "destructive",
      });
      return false;
    } finally {
      setIsSaving(false);
    }
  };

  const navigateBack = () => {
    router.push(`/payments/${paymentId}`);
  };

  const navigateToList = () => {
    router.push('/payments');
  };

  return {
    isSaving,
    savePayment,
    navigateBack,
    navigateToList,
  };
}
