26.07.2025 spor kulübü yönetim sistemi için kapsamlı test senaryolarını içermektedir.
Her modül için farklı test durumları ve sonuçlar belirtilmiştir.

## Test Durumu Açıklaması
- ✅ **Başarılı**: Test tamamlandı ve başarılı
- ❌ **Başarısız**: Test tamamlandı ancak hata bulundu  
- 🔄 **Devam Ediyor**: Test şu anda yapılıyor
- ⏳ **Beklemede**: Test henüz başlamadı
- ⚠️ **Bloke**: Test yapılamıyor (bağımlılık vb.)

---

## 1. GİRİŞ MODÜLÜ (Authentication)

| Test ID | Test Adı | Durum | Test Tarihi | Tester | Notlar |
|---------|----------|-------|-------------|---------|---------|
| TC001 | Geç<PERSON>li kullanıcı ile giriş |✅| | | |
| TC002 | Oturumu sonlandırma |✅| | | |
| TC003 | Oturum süresi kontrolü | ⏳ | | | |
| TC004 | Yetkisiz erişim engelleme |✅| | | |
| TC005 | Hatalı şifre ile erişi kontrolü |✅| | | |


### TC003: Oturum süresi kontrolü
**Test Adımları**:
1. Oturum açık bırak
2. Token süresi dolana kadar bekle
**Beklenen Sonuç**: Otomatik olarak giriş sayfasına yönlendirilmeli

---

## 2. SPORCU YÖNETİMİ (Athletes)

| Test ID | Test Adı | Durum | Test Tarihi | Tester | Notlar |
|---------|----------|-------|-------------|---------|---------|
| TC010 | Yeni sporcu ekle (zorunlu alanlar dolu) | ✅ | | | |
| TC011 | Boş form gönder (validasyon hatası kontrolü) | ✅ | | | |
| TC012 | Geçersiz TC Kimlik No ile sporcu ekleme | ✅ | | | |
WAPP-33=> Geçersiz TC Kimlik No ile sporcu eklemek istediğimizde beklendiği gibi kabul etmiyor 
ancak uyarı olarak geçersiz TC kimlik numarası uyarısı vermesi gerekir. Unexpected error gibi bir hata veriyor.
| TC013 | Sporcu düzenleme | ⏳ | | | |
WAPP-34 => Sporcu düzenle işleminde TC kimlik no geçersiz veya sistemde olan bir TC kimlik no ile değiştirilebiliyor.
| TC014 | Sporcu detay sayfası görüntüleme | ✅ | | | |
| TC015 | Sporcuların borç bakiyesi doğru hesaplanıyor mu? | ✅ | | | |
| TC016 | Sporcuyu aktiften pasife, pasiften aktife alma | ⏳ | | | |
WAPP-35 => Pasif sporcuyu aktife alırken sporcuya ödeme planı atanırken ayın kalan günleri için atama methodu gelmesi lazım.
| TC017 | Sporcu bakiyesini manuel olarak güncelleme | ⏳ | | | |
WAPP-39 => Issue açıldı.


## 3. TAKIM YÖNETİMİ (Teams)

| Test ID | Test Adı | Durum | Test Tarihi | Tester | Notlar |
|---------|----------|-------|-------------|---------|---------|
| TC020 | Yeni takım oluşturma | ❌ | | | |
WAPP-37 issue açıldı. Yeni takım oluştururken “Takım oluşturulurken hata oluştu.” uyarısı alıyoruz 
ve takım oluşturulmuyor. 
Eğitmen atama kısmında eğitmenlerin listesi gelmiyor.
| TC021 | Takım antrenman saati çakışması kontrolü | ✅ | | | |
| TC022 | Takım sporcu atama | ✅ | | | |
| TC023 | Tesis müsaitlik kontrolü | ✅ | | | |
| TC024 | Takım ödeme planı atama | ⏳ | | | |
WAPP-38 => Takımdaki sporcuların mevcut ödeme planının yanında ikinci bir ödeme planı eklenebilmeli.

Örneğin takımdaki sporcunun Aylık Futbol ödeme planı var iken tekrar aylık futbol eklenememeli,
şu anda bu şekide ancak sporcuya farklı bir ödeme planı örneğin aylık fitness veya aylık voleybol atanabilmeli. 

---

## 4. ÖDEME YÖNETİMİ (Payments & Payment Plans)

| Test ID | Test Adı | Durum | Test Tarihi | Tester | Notlar |
|---------|----------|-------|-------------|---------|---------|
| TC030 | Ödeme planı oluşturma | ⏳ | | | |
WAPP-15 => Plan Ön izlemede ingilizce kısımlar var
| TC031 | Sporcuya ödeme planı atama | ✅ | | | |
| TC032 | Prorated ödeme hesaplama | ✅ | | | |
| TC033 | Manuel ödeme ekleme | ✅ | | | |
Prorated ödeme hesaplama kısmında manuel tutar girilebiliyor. Sporcu ekranında manuel tutar girilmesi 
ile ilgili sporcu test adımında bir issue açıldı. 
| TC034 | Ödeme planı deaktifleştirme | ✅ | | | |
| TC035 | Vadesi geçen ödemeler listesi | ✅ | | | |

---

## 5. ANTRENÖR YÖNETİMİ (Instructors)

| Test ID | Test Adı | Durum | Test Tarihi | Tester | Notlar |
|---------|----------|-------|-------------|---------|---------|
| TC040 | Yeni antrenör ekleme | ✅ | | | |
| TC041 | Antrenör branş atama | ✅ | | | |
| TC042 | Antrenörün takımlarını görüntüleme | ⏳ | | | |
Antrenörün takımlarını görüntülemek gerekli mi?

---

## 6. OKUL YÖNETİMİ (Schools)

| Test ID | Test Adı | Durum | Test Tarihi | Tester | Notlar |
|---------|----------|-------|-------------|---------|---------|
| TC050 | Yeni okul ekleme | ✅ | | | |
| TC051 | Okul-takım ilişkisi | ⏳ | | | |
Yeni takım oluştutulamadığı için (WAPP-37) okul takım ilişkisi test edilemedi.
Mevcut takıma ait okul da değiştirilemiyor. 


---

## 7. TESİS YÖNETİMİ (Facilities)

| Test ID | Test Adı | Durum | Test Tarihi | Tester | Notlar |
|---------|----------|-------|-------------|---------|---------|
| TC060 | Yeni tesis ekleme | ✅ | | | |
| TC061 | Tesis antrenman programı kontrolü | ⏳ | | | |
WAPP-40 => Tesis detay görüntüle kısmında tesisin antreman programının görüntülenmesi gerekir.


---

## 8. GİDER YÖNETİMİ (Expenses)

| Test ID | Test Adı | Durum | Test Tarihi | Tester | Notlar |
|---------|----------|-------|-------------|---------|---------|
| TC070 | Yeni gider ekleme | ✅ | | | |
WAPP-45 => Tarih kısmı placeholder’ı ingilizce
| TC071 | Gider kategorisi filtresi | ✅ | | | |
| TC072 | Gider düzenleme | ✅ | | | |
| TC073 | Gider silme | ⏳ | | | |
WAPP-41 => Gider silme işlemi sırasında ingilizce uyarı veriyor. İşlem başarılı bir şekilde sonuçlanıyor.
| TC074 | Tarih aralığı ile gider filtreleme | ✅ | | | |

---

## 9. ÜRÜN/MALZEME YÖNETİMİ (Items)

| Test ID | Test Adı | Durum | Test Tarihi | Tester | Notlar |
|---------|----------|-------|-------------|---------|---------|
| TC080 | Yeni ürün ekleme | ✅ | | | |
WAPP-42 => Menüde Ürünler var ancak panel kısmında kartı yok
| TC081 | Sporcu ürün satışı | ✅ | | | |
WAPP-43 => Ürün Sat Kısmı Miktar Düzenleme 
WAPP-44 => Ürün Sat başarılı bir şekilde yapıldıktan sonra ödemeler ekranında görülen kayıt açıklamasında 
ingilizce  “Purchase: “ yazıyor. 
| TC082 | Stok yetersizliği kontrolü | ✅ | | | |
Satış ekranına stoktakinden fazla değer yazıldığında sat kısmı pasif oluyor. OK
Stoktaki satışların tamamı satıldığında stokta yok yazıyor. OK

---

## 10. DASHBOARD VE RAPORLAMA

| Test ID | Test Adı | Durum | Test Tarihi | Tester | Notlar |
|---------|----------|-------|-------------|---------|---------|
| TC090 | Mali durum grafikleri | ✅ | | | |
| TC091 | Sporcu istatistikleri | ⏳ | | | |
Şu anda yok
| TC092 | Finansal özet kartları | ✅ | | | |


---

## 11. SİSTEM GENELİ TESTLER

| Test ID | Test Adı | Durum | Test Tarihi | Tester | Notlar |
|---------|----------|-------|-------------|---------|---------|
| TC100 | Dil değiştirme (i18n) | ✅ | | | |
| TC101 | Sayfalama (Pagination) | ⏳ | | | |
| TC102 | Arama fonksiyonu | ✅ | | | |
| TC103 | Responsif tasarım | ✅ | | | |
| TC104 | Hata yönetimi | ⏳ | | | |

---

## 12. PERFORMANS TESTLERİ

| Test ID | Test Adı | Durum | Test Tarihi | Tester | Notlar |
|---------|----------|-------|-------------|---------|---------|
| TC110 | Sayfa yükleme hızı | ⏳ | | | |
| TC111 | Büyük veri seti yönetimi | ⏳ | | | |

---

## 13. GÜVENLİK TESTLERİ

| Test ID | Test Adı | Durum | Test Tarihi | Tester | Notlar |
|---------|----------|-------|-------------|---------|---------|
| TC120 | Yetkisiz veri erişimi | ⏳ | | | |
| TC121 | SQL Injection koruması | ⏳ | | | |
| TC122 | XSS koruması | ⏳ | | | |

---

## 14. ÖDEME SCHEDULER TESTLERİ

| Test ID | Test Adı | Durum | Test Tarihi | Tester | Notlar |
|---------|----------|-------|-------------|---------|---------|
| TC130 | Otomatik ödeme oluşturma | ⏳ | | | |
| TC131 | Çoklu ödeme planı yönetimi | ⏳ | | | |
| TC132 | Scheduler hata yönetimi | ⏳ | | | |