# SMS Balance Management Re-enablement Checklist

## Quick Reference Checklist

Use this checklist when you're ready to re-enable SMS balance management after payment integration.

### ✅ Pre-Implementation Checklist

#### Payment Integration Ready
- [ ] Payment processor account created (Stripe/PayPal/Square)
- [ ] API keys obtained (test and production)
- [ ] Webhook endpoints configured
- [ ] SSL certificates installed and verified
- [ ] PCI compliance requirements reviewed

#### Development Environment
- [ ] Payment processor SDK installed (`npm install stripe` or equivalent)
- [ ] Environment variables configured
- [ ] Test payment accounts set up
- [ ] Sandbox/test mode configured

### ✅ Code Changes Checklist

#### Backend Changes
- [ ] **File:** `lib/actions/sms.ts`
  - [ ] Remove early return from `purchaseSmsCredits()` function (line ~391)
  - [ ] Uncomment implementation code in `purchaseSmsCredits()`
  - [ ] Add real payment processor integration
  - [ ] Remove early return from `calculateSmsPrice()` function (line ~358)
  - [ ] Uncomment implementation code in `calculateSmsPrice()`

#### Frontend Changes  
- [ ] **File:** `app/(protected)/sms/balance/sms-balance-form.tsx`
  - [ ] Change `isBalanceManagementDisabled = true` to `false` (line ~37)
  - [ ] Remove or update disabled notice alert

- [ ] **File:** `app/(protected)/sms/sms-page-client.tsx`
  - [ ] Remove warning notice "Payment integration in progress" (line ~238)
  - [ ] Optional: Add success notice "Secure payment processing available"

#### New Files to Create
- [ ] `lib/services/payment-processor.service.ts` - Payment processing logic
- [ ] `lib/types/payment.types.ts` - Payment TypeScript interfaces
- [ ] `lib/utils/payment-validation.ts` - Payment validation utilities

### ✅ Testing Checklist

#### Unit Tests
- [ ] Test payment processing with valid cards
- [ ] Test payment processing with invalid cards
- [ ] Test credit addition after successful payment
- [ ] Test error handling for failed payments
- [ ] Test price calculation accuracy

#### Integration Tests
- [ ] End-to-end payment flow (UI → Payment → Credits)
- [ ] Test with payment processor sandbox
- [ ] Verify balance updates in database
- [ ] Test payment confirmation emails (if implemented)

#### Security Tests
- [ ] Test with invalid/malicious payment data
- [ ] Verify rate limiting on payment endpoints
- [ ] Test CSRF protection
- [ ] Verify SSL/TLS encryption
- [ ] Test input sanitization

### ✅ Deployment Checklist

#### Environment Setup
- [ ] Production payment processor keys configured
- [ ] Environment variables set in production
- [ ] Database migrations applied (if any new tables)
- [ ] SSL certificates verified in production

#### Code Deployment
- [ ] All code changes committed and pushed
- [ ] Build passes all tests
- [ ] Deployment to staging environment
- [ ] Staging environment testing completed
- [ ] Production deployment

#### Post-Deployment Verification
- [ ] Payment form loads correctly
- [ ] Test payment with small amount
- [ ] Verify credits added to balance
- [ ] Check payment processor dashboard
- [ ] Verify no errors in application logs

### ✅ Monitoring Setup

#### Alerts Configuration
- [ ] Payment failure rate alerts (>5% failures)
- [ ] Payment processing time alerts (>10 seconds)
- [ ] Credit addition failure alerts (immediate)
- [ ] High-value transaction alerts (>$100)

#### Dashboards
- [ ] Payment success/failure rates
- [ ] Average payment processing time
- [ ] Daily/weekly revenue tracking
- [ ] User balance distribution

### ✅ Documentation Updates

- [ ] Update user documentation with payment instructions
- [ ] Update API documentation (if applicable)
- [ ] Update deployment documentation
- [ ] Create troubleshooting guide for payment issues

### ✅ Rollback Plan

#### Immediate Rollback (if issues occur)
- [ ] Change `isBalanceManagementDisabled = false` back to `true`
- [ ] Re-add early return statements in backend functions (see code below)
- [ ] Verify error handling in balance form is present
- [ ] Deploy rollback immediately
- [ ] Run build to ensure no TypeScript errors
- [ ] Verify SMS sending still works
- [ ] Notify users of temporary maintenance

#### Full Rollback (if major issues)
- [ ] Revert all code changes to previous version
- [ ] Restore previous environment variables
- [ ] Verify system stability
- [ ] Run full test suite
- [ ] Document issues for future resolution

## Quick Code Changes Summary

### 1. Enable Backend Functions

**File:** `lib/actions/sms.ts`

```typescript
// REMOVE these lines from purchaseSmsCredits():
// SMS balance management is temporarily disabled
logger.warn("purchaseSmsCredits: SMS balance management is disabled");
return {
  success: false,
  error: "SMS balance management is temporarily disabled...",
  errorType: 'feature_disabled',
  data: null
} as any;

// REMOVE these lines from calculateSmsPrice():
// SMS balance management is temporarily disabled
logger.warn("calculateSmsPrice: SMS balance management is disabled");
return {
  success: false,
  error: "SMS balance management is temporarily disabled...",
  errorType: 'feature_disabled',
  data: null
} as any;
```

### 2. Enable Frontend Form

**File:** `app/(protected)/sms/balance/sms-balance-form.tsx`

```typescript
// CHANGE this line:
const isBalanceManagementDisabled = true;

// TO:
const isBalanceManagementDisabled = false;
```

### 3. Update Dashboard Notice

**File:** `app/(protected)/sms/sms-page-client.tsx`

```jsx
// REMOVE or UPDATE this notice:
<p className="text-xs text-amber-600">
  ⚠️ {t('sms:balance.management.disabled')}
</p>

// OPTIONAL: Replace with success notice:
<p className="text-xs text-green-600">
  ✅ Secure payment processing available
</p>
```

### 4. Important: Build Error Prevention

**File:** `app/(protected)/sms/balance/sms-balance-form.tsx`

Ensure proper error handling exists (around line 61):
```typescript
try {
  const result = await calculateSmsPrice(amount);
  if (result.success && result.data) {
    setPriceCalculation(result.data);
  } else {
    // Price calculation failed (likely because balance management is disabled)
    setPriceCalculation(null);
  }
} catch (error) {
  console.error('Error calculating price:', error);
  setPriceCalculation(null);
}
```

**Why this is important:**
- When disabled, backend functions return `{ success: false, data: null }`
- Frontend must handle the case where `result.data` is null
- Without this, you'll get TypeScript/runtime errors

## Emergency Contacts

- **Technical Lead:** [Your Name/Email]
- **Payment Integration:** [Payment Team Contact]
- **Security Team:** [Security Contact]
- **DevOps/Deployment:** [DevOps Contact]

## Related Documentation

- [Full Re-enablement Guide](./sms-balance-management-re-enable.md)
- [Payment Integration Architecture](./payment-integration-architecture.md)
- [SMS System Overview](./sms-system-overview.md)

---

**Last Updated:** December 2024
**Version:** 1.0
**Use this checklist to ensure nothing is missed during re-enablement!**
