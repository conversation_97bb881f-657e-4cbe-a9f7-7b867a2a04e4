# SMS Balance Management Re-enablement Guide

## Overview

This document provides step-by-step instructions to re-enable SMS balance management functionality after payment integration is completed. The SMS balance management was temporarily disabled to prevent users from purchasing credits without a proper payment processor.

## Current State

As of the current implementation:
- ✅ **SMS sending functionality** - Fully operational
- ✅ **Balance display** - Shows current balance
- ✅ **Balance consumption** - Deducts credits when sending SMS
- ❌ **Balance purchasing** - Temporarily disabled
- ❌ **Payment processing** - Temporarily disabled

## Prerequisites

Before re-enabling SMS balance management, ensure you have:

1. **Payment Processor Integration**
   - Credit card processing capability
   - Secure payment gateway (Stripe, PayPal, etc.)
   - PCI compliance if handling card data directly

2. **Security Measures**
   - SSL/TLS certificates
   - Secure API endpoints
   - Input validation and sanitization

3. **Testing Environment**
   - Sandbox/test payment accounts
   - Test credit card numbers
   - Comprehensive testing procedures

## Step-by-Step Re-enablement Process

### Step 1: Backend Function Re-enablement

#### 1.1 Re-enable Purchase Credits Function

**File:** `lib/actions/sms.ts`

**Current State:**
```typescript
export async function purchaseSmsCredits(data: {
  credits: number;
  expectedPrice: number;
  paymentData: {
    cardNumber: string;
    expiryDate: string;
    cvv: string;
    cardHolder: string;
  };
}) {
  // SMS balance management is temporarily disabled
  logger.warn("purchaseSmsCredits: SMS balance management is disabled");
  return { 
    success: false, 
    error: "SMS balance management is temporarily disabled. Please contact administrator for manual balance updates.", 
    errorType: 'feature_disabled' 
  };
  // ... commented out code
}
```

**To Re-enable:**
1. Remove the early return statement
2. Uncomment the implementation code
3. Replace the TODO comment with actual payment processor integration

**Updated Code:**
```typescript
export async function purchaseSmsCredits(data: {
  credits: number;
  expectedPrice: number;
  paymentData: {
    cardNumber: string;
    expiryDate: string;
    cvv: string;
    cardHolder: string;
  };
}) {
  try {
    const tenantId = await getServerTenantId();
    const userId = await getServerUserId();

    // Validate purchase server-side
    const validation = await smsPricingService().validatePurchase(data.credits, data.expectedPrice);

    if (!validation.success) {
      logger.error("purchaseSmsCredits validation error:", { error: validation.error });
      throw new Error(validation.error?.userMessage || "Purchase validation failed");
    }

    // INTEGRATE WITH REAL PAYMENT PROCESSOR HERE
    const paymentResult = await processPayment({
      amount: data.expectedPrice,
      currency: 'USD', // or your preferred currency
      paymentData: data.paymentData,
      description: `SMS Credits Purchase - ${data.credits} credits`
    });

    if (!paymentResult.success) {
      throw new Error(paymentResult.error || "Payment processing failed");
    }

    // Add credits to balance after successful payment
    const result = await smsBalanceService().addSmsCredits(
      data.credits,
      userId?.toString(),
      tenantId || undefined
    );

    if (!result.success) {
      // Payment succeeded but credit addition failed - this needs manual intervention
      logger.error("CRITICAL: Payment succeeded but credit addition failed", {
        paymentId: paymentResult.paymentId,
        credits: data.credits,
        tenantId,
        error: result.error
      });
      
      // You might want to implement a refund mechanism here
      throw new Error("Payment processed but credit addition failed. Support has been notified.");
    }

    revalidatePath('/sms/balance');
    return { success: true, data: result.data };
  } catch (error) {
    logger.error("Error purchasing SMS credits:", { error });
    return { success: false, error: error instanceof Error ? error.message : "Failed to purchase SMS credits", errorType: 'general' };
  }
}
```

#### 1.2 Re-enable Price Calculation Function

**File:** `lib/actions/sms.ts`

**Current State:**
```typescript
export async function calculateSmsPrice(credits: number) {
  // SMS balance management is temporarily disabled
  logger.warn("calculateSmsPrice: SMS balance management is disabled");
  return { 
    success: false, 
    error: "SMS balance management is temporarily disabled. Please contact administrator for manual balance updates.", 
    errorType: 'feature_disabled' 
  };
  // ... commented out code
}
```

**To Re-enable:**
```typescript
export async function calculateSmsPrice(credits: number) {
  try {
    const result = await smsPricingService().calculatePurchasePrice(credits);

    if (!result.success) {
      logger.error("calculateSmsPrice error:", { error: result.error });
      throw new Error(result.error?.userMessage || "Failed to calculate SMS price");
    }

    return { success: true, data: result.data };
  } catch (error) {
    logger.error("Error calculating SMS price:", { error });
    throw error;
  }
}
```

### Step 2: Frontend Re-enablement

#### 2.1 Re-enable Balance Form

**File:** `app/(protected)/sms/balance/sms-balance-form.tsx`

**Current State:**
```typescript
// SMS balance management is temporarily disabled
const isBalanceManagementDisabled = true;
```

**To Re-enable:**
```typescript
// SMS balance management is now enabled
const isBalanceManagementDisabled = false;
```

#### 2.2 Update SMS Dashboard Notices

**File:** `app/(protected)/sms/sms-page-client.tsx`

**Current State:**
```jsx
<p className="text-xs text-amber-600">
  ⚠️ {t('sms:balance.management.disabled')}
</p>
```

**To Re-enable:**
Remove the warning notice or replace with:
```jsx
<p className="text-xs text-green-600">
  ✅ Secure payment processing available
</p>
```

### Step 3: Payment Integration Implementation

#### 3.1 Create Payment Processor Service

**File:** `lib/services/payment-processor.service.ts` (Create new file)

```typescript
// Example implementation for Stripe
import Stripe from 'stripe';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2023-10-16',
});

export interface PaymentData {
  amount: number;
  currency: string;
  paymentData: {
    cardNumber: string;
    expiryDate: string;
    cvv: string;
    cardHolder: string;
  };
  description: string;
}

export async function processPayment(data: PaymentData) {
  try {
    // Create payment method
    const paymentMethod = await stripe.paymentMethods.create({
      type: 'card',
      card: {
        number: data.paymentData.cardNumber,
        exp_month: parseInt(data.paymentData.expiryDate.split('/')[0]),
        exp_year: parseInt(data.paymentData.expiryDate.split('/')[1]),
        cvc: data.paymentData.cvv,
      },
      billing_details: {
        name: data.paymentData.cardHolder,
      },
    });

    // Create payment intent
    const paymentIntent = await stripe.paymentIntents.create({
      amount: Math.round(data.amount * 100), // Convert to cents
      currency: data.currency.toLowerCase(),
      payment_method: paymentMethod.id,
      confirm: true,
      description: data.description,
    });

    if (paymentIntent.status === 'succeeded') {
      return {
        success: true,
        paymentId: paymentIntent.id,
        amount: paymentIntent.amount / 100,
      };
    } else {
      return {
        success: false,
        error: 'Payment failed',
      };
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Payment processing failed',
    };
  }
}
```

#### 3.2 Environment Variables

Add to your `.env` file:
```env
# Payment Processing
STRIPE_SECRET_KEY=sk_test_...
STRIPE_PUBLISHABLE_KEY=pk_test_...

# Or for other payment processors
PAYPAL_CLIENT_ID=...
PAYPAL_CLIENT_SECRET=...
```

### Step 4: Security Considerations

#### 4.1 Input Validation
- Validate all payment data on server-side
- Sanitize credit card information
- Implement rate limiting for payment attempts

#### 4.2 Error Handling
- Implement proper error logging
- Handle partial failures (payment succeeded but credit addition failed)
- Implement refund mechanisms for failed credit additions

#### 4.3 Audit Trail
- Log all payment attempts
- Track successful and failed transactions
- Maintain payment history for users

### Step 5: Testing Procedures

#### 5.1 Unit Tests
- Test payment processing with valid/invalid cards
- Test credit addition after successful payment
- Test error handling scenarios

#### 5.2 Integration Tests
- End-to-end payment flow testing
- Test with payment processor sandbox
- Verify balance updates after purchase

#### 5.3 Security Tests
- Test with invalid payment data
- Test rate limiting
- Test SQL injection and XSS prevention

### Step 6: Deployment Checklist

- [ ] Payment processor credentials configured
- [ ] SSL certificates installed and verified
- [ ] All environment variables set
- [ ] Database migrations completed (if any)
- [ ] Frontend re-enablement flags updated
- [ ] Backend function comments removed
- [ ] Comprehensive testing completed
- [ ] Error monitoring configured
- [ ] Payment webhook endpoints configured (if applicable)
- [ ] Backup and rollback procedures documented

## Rollback Procedures

If issues arise after re-enablement:

1. **Immediate Frontend Rollback:**
   ```typescript
   // In app/(protected)/sms/balance/sms-balance-form.tsx
   const isBalanceManagementDisabled = true;
   ```

2. **Backend Rollback:**
   Re-add the early return statements in payment functions:

   **File:** `lib/actions/sms.ts`
   ```typescript
   // In purchaseSmsCredits function (around line 401):
   // SMS balance management is temporarily disabled
   logger.warn("purchaseSmsCredits: SMS balance management is disabled");
   return {
     success: false,
     error: "SMS balance management is temporarily disabled. Please contact administrator for manual balance updates.",
     errorType: 'feature_disabled',
     data: null
   } as any;

   // In calculateSmsPrice function (around line 360):
   // SMS balance management is temporarily disabled
   logger.warn("calculateSmsPrice: SMS balance management is disabled");
   return {
     success: false,
     error: "SMS balance management is temporarily disabled. Please contact administrator for manual balance updates.",
     errorType: 'feature_disabled',
     data: null
   } as any;
   ```

3. **Frontend Error Handling Rollback:**
   Ensure proper error handling in balance form:

   **File:** `app/(protected)/sms/balance/sms-balance-form.tsx`
   ```typescript
   // Around line 61, ensure this error handling exists:
   try {
     const result = await calculateSmsPrice(amount);
     if (result.success && result.data) {
       setPriceCalculation(result.data);
     } else {
       // Price calculation failed (likely because balance management is disabled)
       setPriceCalculation(null);
     }
   } catch (error) {
     console.error('Error calculating price:', error);
     setPriceCalculation(null);
   }
   ```

4. **Database Rollback:**
   - No database changes should be needed for rollback
   - Existing balances remain intact

5. **Verification After Rollback:**
   - [ ] SMS sending still works with existing balance
   - [ ] Balance display shows current balance correctly
   - [ ] Balance management forms show disabled state
   - [ ] No build errors or TypeScript issues
   - [ ] All SMS functionality except purchasing works normally

## Support and Maintenance

- Monitor payment success/failure rates
- Set up alerts for payment processing errors
- Regular security audits of payment handling code
- Keep payment processor SDKs updated
- Monitor for PCI compliance requirements

## Contact Information

For questions about this re-enablement process:
- Technical Lead: [Your Name]
- Payment Integration: [Payment Team]
- Security Review: [Security Team]

## Appendix A: File Locations and Changes Summary

### Files Modified for Disabling
1. `lib/actions/sms.ts` - Lines 391-447 (purchaseSmsCredits function)
2. `lib/actions/sms.ts` - Lines 358-383 (calculateSmsPrice function)
3. `app/(protected)/sms/balance/sms-balance-form.tsx` - Line 37 (isBalanceManagementDisabled flag)
4. `app/(protected)/sms/sms-page-client.tsx` - Line 238 (warning notice)

### Files to Create for Payment Integration
1. `lib/services/payment-processor.service.ts` - Payment processing logic
2. `lib/types/payment.types.ts` - Payment-related TypeScript interfaces
3. `components/sms/payment-confirmation-dialog.tsx` - Enhanced payment confirmation
4. `lib/utils/payment-validation.ts` - Payment data validation utilities

## Appendix B: Database Considerations

### Current SMS Balance Schema
```sql
-- SMS Balance table structure
CREATE TABLE sms_balance (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id UUID NOT NULL REFERENCES tenants(id),
  balance INTEGER NOT NULL DEFAULT 0,
  last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by BIGINT NOT NULL,
  updated_by BIGINT NOT NULL
);
```

### Recommended Payment History Table
```sql
-- Payment history for audit trail
CREATE TABLE sms_payment_history (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id UUID NOT NULL REFERENCES tenants(id),
  user_id BIGINT NOT NULL,
  credits_purchased INTEGER NOT NULL,
  amount_paid DECIMAL(10,2) NOT NULL,
  currency VARCHAR(3) NOT NULL DEFAULT 'USD',
  payment_processor VARCHAR(50) NOT NULL,
  payment_id VARCHAR(255) NOT NULL, -- External payment processor ID
  payment_status VARCHAR(20) NOT NULL, -- succeeded, failed, pending, refunded
  payment_method VARCHAR(50), -- card, paypal, etc.
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## Appendix C: Environment Variables Reference

### Required Environment Variables
```env
# Database
DATABASE_URL=postgresql://...

# SMS Provider (NetGSM)
NETGSM_USERNAME=your_username
NETGSM_PASSWORD=your_password

# Payment Processor (Choose one)
# Stripe
STRIPE_SECRET_KEY=sk_live_... # or sk_test_... for testing
STRIPE_PUBLISHABLE_KEY=pk_live_... # or pk_test_... for testing
STRIPE_WEBHOOK_SECRET=whsec_...

# PayPal
PAYPAL_CLIENT_ID=...
PAYPAL_CLIENT_SECRET=...
PAYPAL_ENVIRONMENT=sandbox # or live

# Square
SQUARE_ACCESS_TOKEN=...
SQUARE_APPLICATION_ID=...
SQUARE_ENVIRONMENT=sandbox # or production

# Security
NEXTAUTH_SECRET=your_secret_key
NEXTAUTH_URL=https://yourdomain.com

# Application
NODE_ENV=production
```

## Appendix D: Testing Scenarios

### Payment Testing Checklist

#### Valid Payment Scenarios
- [ ] Valid credit card with sufficient funds
- [ ] Different credit amounts (100, 500, 1000, 5000 credits)
- [ ] Different payment methods (Visa, MasterCard, American Express)
- [ ] International cards (if supported)

#### Invalid Payment Scenarios
- [ ] Expired credit card
- [ ] Insufficient funds
- [ ] Invalid card number
- [ ] Invalid CVV
- [ ] Invalid expiry date
- [ ] Blocked/declined card

#### Edge Cases
- [ ] Network timeout during payment
- [ ] Payment succeeds but credit addition fails
- [ ] Duplicate payment attempts
- [ ] Concurrent payment requests
- [ ] Very large credit purchases (>10,000)
- [ ] Very small credit purchases (<10)

#### Security Testing
- [ ] SQL injection attempts in payment data
- [ ] XSS attempts in cardholder name
- [ ] Rate limiting on payment endpoints
- [ ] CSRF protection verification
- [ ] SSL/TLS certificate validation

## Appendix E: Monitoring and Alerts

### Key Metrics to Monitor
1. **Payment Success Rate** - Should be >95%
2. **Average Payment Processing Time** - Should be <5 seconds
3. **Failed Payment Reasons** - Track common failure causes
4. **Credit Addition Success Rate** - Should be 100%
5. **User Balance Accuracy** - Regular balance audits

### Recommended Alerts
```javascript
// Example alert configurations
{
  "payment_failure_rate": {
    "threshold": "5%",
    "window": "1 hour",
    "action": "notify_team"
  },
  "payment_processing_time": {
    "threshold": "10 seconds",
    "window": "5 minutes",
    "action": "investigate"
  },
  "credit_addition_failure": {
    "threshold": "1 failure",
    "window": "immediate",
    "action": "urgent_alert"
  }
}
```

---

**Last Updated:** December 2024
**Version:** 1.1
**Status:** Ready for Implementation
**Reviewed By:** Development Team
