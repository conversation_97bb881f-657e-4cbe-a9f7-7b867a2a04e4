{"id": "ebce59d1-7276-4789-b163-9d28777aab31", "prevId": "e0423454-d814-423a-91f5-8bd1c766a7ad", "version": "7", "dialect": "postgresql", "tables": {"public.athlete_payment_plans": {"name": "athlete_payment_plans", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "athlete_id": {"name": "athlete_id", "type": "uuid", "primaryKey": false, "notNull": true}, "plan_id": {"name": "plan_id", "type": "uuid", "primaryKey": false, "notNull": true}, "team_id": {"name": "team_id", "type": "uuid", "primaryKey": false, "notNull": false}, "assigned_date": {"name": "assigned_date", "type": "date", "primaryKey": false, "notNull": true, "default": "now()"}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "last_payment_date": {"name": "last_payment_date", "type": "date", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_by": {"name": "created_by", "type": "bigint", "primaryKey": false, "notNull": true}, "updated_by": {"name": "updated_by", "type": "bigint", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"athlete_payment_plans_athlete_id_athletes_id_fk": {"name": "athlete_payment_plans_athlete_id_athletes_id_fk", "tableFrom": "athlete_payment_plans", "tableTo": "athletes", "columnsFrom": ["athlete_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "athlete_payment_plans_plan_id_payment_plans_id_fk": {"name": "athlete_payment_plans_plan_id_payment_plans_id_fk", "tableFrom": "athlete_payment_plans", "tableTo": "payment_plans", "columnsFrom": ["plan_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "athlete_payment_plans_team_id_teams_id_fk": {"name": "athlete_payment_plans_team_id_teams_id_fk", "tableFrom": "athlete_payment_plans", "tableTo": "teams", "columnsFrom": ["team_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.athlete_teams": {"name": "athlete_teams", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "athlete_id": {"name": "athlete_id", "type": "uuid", "primaryKey": false, "notNull": true}, "team_id": {"name": "team_id", "type": "uuid", "primaryKey": false, "notNull": true}, "joined_at": {"name": "joined_at", "type": "date", "primaryKey": false, "notNull": true, "default": "now()"}, "left_at": {"name": "left_at", "type": "date", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"athlete_teams_athlete_id_athletes_id_fk": {"name": "athlete_teams_athlete_id_athletes_id_fk", "tableFrom": "athlete_teams", "tableTo": "athletes", "columnsFrom": ["athlete_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "athlete_teams_team_id_teams_id_fk": {"name": "athlete_teams_team_id_teams_id_fk", "tableFrom": "athlete_teams", "tableTo": "teams", "columnsFrom": ["team_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.athletes": {"name": "athletes", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "surname": {"name": "surname", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "national_id": {"name": "national_id", "type": "<PERSON><PERSON><PERSON>(11)", "primaryKey": false, "notNull": true}, "birth_date": {"name": "birth_date", "type": "date", "primaryKey": false, "notNull": true}, "registration_date": {"name": "registration_date", "type": "date", "primaryKey": false, "notNull": true, "default": "now()"}, "status": {"name": "status", "type": "athlete_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'active'"}, "balance": {"name": "balance", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true, "default": "'0'"}, "parent_name": {"name": "parent_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "parent_surname": {"name": "parent_surname", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "parent_phone": {"name": "parent_phone", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "parent_email": {"name": "parent_email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "parent_address": {"name": "parent_address", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_by": {"name": "created_by", "type": "bigint", "primaryKey": false, "notNull": true}, "updated_by": {"name": "updated_by", "type": "bigint", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"athletes_national_id_unique": {"name": "athletes_national_id_unique", "nullsNotDistinct": false, "columns": ["national_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.branches": {"name": "branches", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "long_description": {"name": "long_description", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_by": {"name": "created_by", "type": "bigint", "primaryKey": false, "notNull": true}, "updated_by": {"name": "updated_by", "type": "bigint", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.expenses": {"name": "expenses", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "amount": {"name": "amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "date": {"name": "date", "type": "date", "primaryKey": false, "notNull": true}, "category": {"name": "category", "type": "expense_category", "typeSchema": "public", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "instructor_id": {"name": "instructor_id", "type": "uuid", "primaryKey": false, "notNull": false}, "facility_id": {"name": "facility_id", "type": "uuid", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_by": {"name": "created_by", "type": "bigint", "primaryKey": false, "notNull": true}, "updated_by": {"name": "updated_by", "type": "bigint", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"expenses_instructor_id_instructors_id_fk": {"name": "expenses_instructor_id_instructors_id_fk", "tableFrom": "expenses", "tableTo": "instructors", "columnsFrom": ["instructor_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "expenses_facility_id_facilities_id_fk": {"name": "expenses_facility_id_facilities_id_fk", "tableFrom": "expenses", "tableTo": "facilities", "columnsFrom": ["facility_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.facilities": {"name": "facilities", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "facility_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": true}, "total_capacity": {"name": "total_capacity", "type": "integer", "primaryKey": false, "notNull": false}, "currently_occupied": {"name": "currently_occupied", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "length": {"name": "length", "type": "numeric(8, 2)", "primaryKey": false, "notNull": false}, "width": {"name": "width", "type": "numeric(8, 2)", "primaryKey": false, "notNull": false}, "dimension_unit": {"name": "dimension_unit", "type": "dimension_unit", "typeSchema": "public", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_by": {"name": "created_by", "type": "bigint", "primaryKey": false, "notNull": true}, "updated_by": {"name": "updated_by", "type": "bigint", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.instructor_branches": {"name": "instructor_branches", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "instructor_id": {"name": "instructor_id", "type": "uuid", "primaryKey": false, "notNull": true}, "branch_id": {"name": "branch_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"instructor_branches_instructor_id_instructors_id_fk": {"name": "instructor_branches_instructor_id_instructors_id_fk", "tableFrom": "instructor_branches", "tableTo": "instructors", "columnsFrom": ["instructor_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "instructor_branches_branch_id_branches_id_fk": {"name": "instructor_branches_branch_id_branches_id_fk", "tableFrom": "instructor_branches", "tableTo": "branches", "columnsFrom": ["branch_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.instructor_schools": {"name": "instructor_schools", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "instructor_id": {"name": "instructor_id", "type": "uuid", "primaryKey": false, "notNull": true}, "school_id": {"name": "school_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"instructor_schools_instructor_id_instructors_id_fk": {"name": "instructor_schools_instructor_id_instructors_id_fk", "tableFrom": "instructor_schools", "tableTo": "instructors", "columnsFrom": ["instructor_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "instructor_schools_school_id_schools_id_fk": {"name": "instructor_schools_school_id_schools_id_fk", "tableFrom": "instructor_schools", "tableTo": "schools", "columnsFrom": ["school_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.instructors": {"name": "instructors", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "surname": {"name": "surname", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "national_id": {"name": "national_id", "type": "<PERSON><PERSON><PERSON>(11)", "primaryKey": false, "notNull": false}, "birth_date": {"name": "birth_date", "type": "date", "primaryKey": false, "notNull": false}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": false}, "salary": {"name": "salary", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_by": {"name": "created_by", "type": "bigint", "primaryKey": false, "notNull": true}, "updated_by": {"name": "updated_by", "type": "bigint", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.item_purchases": {"name": "item_purchases", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "item_id": {"name": "item_id", "type": "uuid", "primaryKey": false, "notNull": true}, "athlete_id": {"name": "athlete_id", "type": "uuid", "primaryKey": false, "notNull": true}, "quantity": {"name": "quantity", "type": "integer", "primaryKey": false, "notNull": true}, "total_price": {"name": "total_price", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "purchase_date": {"name": "purchase_date", "type": "date", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_by": {"name": "created_by", "type": "bigint", "primaryKey": false, "notNull": true}, "updated_by": {"name": "updated_by", "type": "bigint", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"item_purchases_item_id_items_id_fk": {"name": "item_purchases_item_id_items_id_fk", "tableFrom": "item_purchases", "tableTo": "items", "columnsFrom": ["item_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "item_purchases_athlete_id_athletes_id_fk": {"name": "item_purchases_athlete_id_athletes_id_fk", "tableFrom": "item_purchases", "tableTo": "athletes", "columnsFrom": ["athlete_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.items": {"name": "items", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "price": {"name": "price", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "category": {"name": "category", "type": "item_category", "typeSchema": "public", "primaryKey": false, "notNull": true}, "stock": {"name": "stock", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_by": {"name": "created_by", "type": "bigint", "primaryKey": false, "notNull": true}, "updated_by": {"name": "updated_by", "type": "bigint", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.payment_plan_branches": {"name": "payment_plan_branches", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "payment_plan_id": {"name": "payment_plan_id", "type": "uuid", "primaryKey": false, "notNull": true}, "branch_id": {"name": "branch_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"payment_plan_branches_payment_plan_id_payment_plans_id_fk": {"name": "payment_plan_branches_payment_plan_id_payment_plans_id_fk", "tableFrom": "payment_plan_branches", "tableTo": "payment_plans", "columnsFrom": ["payment_plan_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payment_plan_branches_branch_id_branches_id_fk": {"name": "payment_plan_branches_branch_id_branches_id_fk", "tableFrom": "payment_plan_branches", "tableTo": "branches", "columnsFrom": ["branch_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"payment_plan_branches_payment_plan_id_branch_id_unique": {"name": "payment_plan_branches_payment_plan_id_branch_id_unique", "nullsNotDistinct": false, "columns": ["payment_plan_id", "branch_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.payment_plans": {"name": "payment_plans", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "monthly_value": {"name": "monthly_value", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "assign_day": {"name": "assign_day", "type": "integer", "primaryKey": false, "notNull": true}, "due_day": {"name": "due_day", "type": "integer", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "payment_plan_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'active'"}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_by": {"name": "created_by", "type": "bigint", "primaryKey": false, "notNull": true}, "updated_by": {"name": "updated_by", "type": "bigint", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.payment_transactions": {"name": "payment_transactions", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "athlete_id": {"name": "athlete_id", "type": "uuid", "primaryKey": false, "notNull": true}, "payment_id": {"name": "payment_id", "type": "uuid", "primaryKey": false, "notNull": false}, "amount": {"name": "amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "transaction_method": {"name": "transaction_method", "type": "transaction_method", "typeSchema": "public", "primaryKey": false, "notNull": true}, "transaction_date": {"name": "transaction_date", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_by": {"name": "created_by", "type": "bigint", "primaryKey": false, "notNull": true}, "updated_by": {"name": "updated_by", "type": "bigint", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"payment_transactions_athlete_id_athletes_id_fk": {"name": "payment_transactions_athlete_id_athletes_id_fk", "tableFrom": "payment_transactions", "tableTo": "athletes", "columnsFrom": ["athlete_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payment_transactions_payment_id_payments_id_fk": {"name": "payment_transactions_payment_id_payments_id_fk", "tableFrom": "payment_transactions", "tableTo": "payments", "columnsFrom": ["payment_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.payments": {"name": "payments", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "athlete_id": {"name": "athlete_id", "type": "uuid", "primaryKey": false, "notNull": true}, "athlete_payment_plan_id": {"name": "athlete_payment_plan_id", "type": "uuid", "primaryKey": false, "notNull": false}, "item_purchase_id": {"name": "item_purchase_id", "type": "uuid", "primaryKey": false, "notNull": false}, "amount": {"name": "amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "amount_paid": {"name": "amount_paid", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true, "default": "'0'"}, "date": {"name": "date", "type": "date", "primaryKey": false, "notNull": true}, "due_date": {"name": "due_date", "type": "date", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "payment_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'pending'"}, "type": {"name": "type", "type": "payment_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_by": {"name": "created_by", "type": "bigint", "primaryKey": false, "notNull": true}, "updated_by": {"name": "updated_by", "type": "bigint", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"payments_athlete_id_athletes_id_fk": {"name": "payments_athlete_id_athletes_id_fk", "tableFrom": "payments", "tableTo": "athletes", "columnsFrom": ["athlete_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payments_athlete_payment_plan_id_athlete_payment_plans_id_fk": {"name": "payments_athlete_payment_plan_id_athlete_payment_plans_id_fk", "tableFrom": "payments", "tableTo": "athlete_payment_plans", "columnsFrom": ["athlete_payment_plan_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "payments_item_purchase_id_item_purchases_id_fk": {"name": "payments_item_purchase_id_item_purchases_id_fk", "tableFrom": "payments", "tableTo": "item_purchases", "columnsFrom": ["item_purchase_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.school_branches": {"name": "school_branches", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "school_id": {"name": "school_id", "type": "uuid", "primaryKey": false, "notNull": true}, "branch_id": {"name": "branch_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"school_branches_school_id_schools_id_fk": {"name": "school_branches_school_id_schools_id_fk", "tableFrom": "school_branches", "tableTo": "schools", "columnsFrom": ["school_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "school_branches_branch_id_branches_id_fk": {"name": "school_branches_branch_id_branches_id_fk", "tableFrom": "school_branches", "tableTo": "branches", "columnsFrom": ["branch_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.schools": {"name": "schools", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "founded_year": {"name": "founded_year", "type": "integer", "primaryKey": false, "notNull": true}, "logo": {"name": "logo", "type": "text", "primaryKey": false, "notNull": false}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": false}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_by": {"name": "created_by", "type": "bigint", "primaryKey": false, "notNull": true}, "updated_by": {"name": "updated_by", "type": "bigint", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.sms_balance": {"name": "sms_balance", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "balance": {"name": "balance", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "last_updated": {"name": "last_updated", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_by": {"name": "created_by", "type": "bigint", "primaryKey": false, "notNull": true}, "updated_by": {"name": "updated_by", "type": "bigint", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"sms_balance_tenant_id_unique": {"name": "sms_balance_tenant_id_unique", "nullsNotDistinct": false, "columns": ["tenant_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.sms_configurations": {"name": "sms_configurations", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "version": {"name": "version", "type": "integer", "primaryKey": false, "notNull": true}, "pending_payment_template": {"name": "pending_payment_template", "type": "text", "primaryKey": false, "notNull": true}, "overdue_payment_template": {"name": "overdue_payment_template", "type": "text", "primaryKey": false, "notNull": true}, "pending_reminder_days": {"name": "pending_reminder_days", "type": "text", "primaryKey": false, "notNull": true}, "overdue_reminder_days": {"name": "overdue_reminder_days", "type": "text", "primaryKey": false, "notNull": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_by": {"name": "created_by", "type": "bigint", "primaryKey": false, "notNull": true}, "updated_by": {"name": "updated_by", "type": "bigint", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.sms_log_payments": {"name": "sms_log_payments", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "sms_log_id": {"name": "sms_log_id", "type": "uuid", "primaryKey": false, "notNull": true}, "payment_id": {"name": "payment_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"sms_log_payments_sms_log_id_sms_logs_id_fk": {"name": "sms_log_payments_sms_log_id_sms_logs_id_fk", "tableFrom": "sms_log_payments", "tableTo": "sms_logs", "columnsFrom": ["sms_log_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "sms_log_payments_payment_id_payments_id_fk": {"name": "sms_log_payments_payment_id_payments_id_fk", "tableFrom": "sms_log_payments", "tableTo": "payments", "columnsFrom": ["payment_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"sms_log_payments_sms_log_id_payment_id_unique": {"name": "sms_log_payments_sms_log_id_payment_id_unique", "nullsNotDistinct": false, "columns": ["sms_log_id", "payment_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.sms_logs": {"name": "sms_logs", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "sms_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "sms_status", "typeSchema": "public", "primaryKey": false, "notNull": true}, "receiver": {"name": "receiver", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "message": {"name": "message", "type": "text", "primaryKey": false, "notNull": true}, "sender_identifier": {"name": "sender_identifier", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "sent_at": {"name": "sent_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "credits_used": {"name": "credits_used", "type": "integer", "primaryKey": false, "notNull": true, "default": 1}, "provider_response": {"name": "provider_response", "type": "text", "primaryKey": false, "notNull": false}, "athlete_id": {"name": "athlete_id", "type": "uuid", "primaryKey": false, "notNull": false}, "team_id": {"name": "team_id", "type": "uuid", "primaryKey": false, "notNull": false}, "sender_type": {"name": "sender_type", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "sender_id": {"name": "sender_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"sms_logs_athlete_id_athletes_id_fk": {"name": "sms_logs_athlete_id_athletes_id_fk", "tableFrom": "sms_logs", "tableTo": "athletes", "columnsFrom": ["athlete_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "sms_logs_team_id_teams_id_fk": {"name": "sms_logs_team_id_teams_id_fk", "tableFrom": "sms_logs", "tableTo": "teams", "columnsFrom": ["team_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.sms_pricing_tiers": {"name": "sms_pricing_tiers", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "min_credits": {"name": "min_credits", "type": "integer", "primaryKey": false, "notNull": true}, "max_credits": {"name": "max_credits", "type": "integer", "primaryKey": false, "notNull": false}, "price_per_credit": {"name": "price_per_credit", "type": "integer", "primaryKey": false, "notNull": true}, "currency": {"name": "currency", "type": "<PERSON><PERSON><PERSON>(3)", "primaryKey": false, "notNull": true, "default": "'USD'"}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "sort_order": {"name": "sort_order", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_by": {"name": "created_by", "type": "bigint", "primaryKey": false, "notNull": true}, "updated_by": {"name": "updated_by", "type": "bigint", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.teams": {"name": "teams", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "school_id": {"name": "school_id", "type": "uuid", "primaryKey": false, "notNull": true}, "branch_id": {"name": "branch_id", "type": "uuid", "primaryKey": false, "notNull": true}, "instructor_id": {"name": "instructor_id", "type": "uuid", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_by": {"name": "created_by", "type": "bigint", "primaryKey": false, "notNull": true}, "updated_by": {"name": "updated_by", "type": "bigint", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"teams_school_id_schools_id_fk": {"name": "teams_school_id_schools_id_fk", "tableFrom": "teams", "tableTo": "schools", "columnsFrom": ["school_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "teams_branch_id_branches_id_fk": {"name": "teams_branch_id_branches_id_fk", "tableFrom": "teams", "tableTo": "branches", "columnsFrom": ["branch_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "teams_instructor_id_instructors_id_fk": {"name": "teams_instructor_id_instructors_id_fk", "tableFrom": "teams", "tableTo": "instructors", "columnsFrom": ["instructor_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.training_schedules": {"name": "training_schedules", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "team_id": {"name": "team_id", "type": "uuid", "primaryKey": false, "notNull": true}, "facility_id": {"name": "facility_id", "type": "uuid", "primaryKey": false, "notNull": true}, "day_of_week": {"name": "day_of_week", "type": "integer", "primaryKey": false, "notNull": true}, "start_time": {"name": "start_time", "type": "time", "primaryKey": false, "notNull": true}, "end_time": {"name": "end_time", "type": "time", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_by": {"name": "created_by", "type": "bigint", "primaryKey": false, "notNull": true}, "updated_by": {"name": "updated_by", "type": "bigint", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"training_schedules_team_id_teams_id_fk": {"name": "training_schedules_team_id_teams_id_fk", "tableFrom": "training_schedules", "tableTo": "teams", "columnsFrom": ["team_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "training_schedules_facility_id_facilities_id_fk": {"name": "training_schedules_facility_id_facilities_id_fk", "tableFrom": "training_schedules", "tableTo": "facilities", "columnsFrom": ["facility_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.athlete_status": {"name": "athlete_status", "schema": "public", "values": ["active", "inactive", "suspended"]}, "public.dimension_unit": {"name": "dimension_unit", "schema": "public", "values": ["meters", "feet"]}, "public.expense_category": {"name": "expense_category", "schema": "public", "values": ["salary", "insurance", "rent", "equipment", "other"]}, "public.facility_type": {"name": "facility_type", "schema": "public", "values": ["field", "court", "pool", "studio", "other"]}, "public.item_category": {"name": "item_category", "schema": "public", "values": ["equipment", "clothing", "accessories", "other"]}, "public.payment_plan_status": {"name": "payment_plan_status", "schema": "public", "values": ["active", "inactive"]}, "public.payment_status": {"name": "payment_status", "schema": "public", "values": ["pending", "completed", "overdue", "cancelled", "partially_paid"]}, "public.payment_type": {"name": "payment_type", "schema": "public", "values": ["fee", "equipment", "other"]}, "public.sms_status": {"name": "sms_status", "schema": "public", "values": ["pending", "sent", "failed", "cancelled"]}, "public.sms_type": {"name": "sms_type", "schema": "public", "values": ["payment_reminder", "team_message", "custom"]}, "public.transaction_method": {"name": "transaction_method", "schema": "public", "values": ["cash", "bank_transfer", "credit_card", "existing_balance"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}