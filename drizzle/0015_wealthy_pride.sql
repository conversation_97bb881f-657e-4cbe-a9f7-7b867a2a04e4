CREATE TYPE "public"."sms_template_type" AS ENUM('general', 'event', 'meeting', 'holiday', 'custom');--> statement-breakpoint
CREATE TABLE "sms_templates" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" varchar(255) NOT NULL,
	"type" "sms_template_type" NOT NULL,
	"language" varchar(5) NOT NULL,
	"title" varchar(255) NOT NULL,
	"template" text NOT NULL,
	"is_active" boolean DEFAULT true NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"created_by" bigint NOT NULL,
	"updated_by" bigint NOT NULL,
	CONSTRAINT "unique_active_template" UNIQUE("tenant_id","type","language","is_active")
);
