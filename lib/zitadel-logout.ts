import { signOut } from "next-auth/react";
import { serverLogout } from '@/lib/actions/auth';

/**
 * Securely logs out the user by calling the server-side logout endpoint
 * This function will:
 * 1. Call the secure server-side logout API
 * 2. Clear the NextAuth session
 * 3. Redirect to Zitadel's logout endpoint for complete OIDC logout
 */
export async function logoutFromZitadel() {
  try {
    const result = await serverLogout();

    if (!result.success) {
      throw new Error(result.error || 'Server-side logout failed');
    }

    // Clear NextAuth session (without redirect to prevent auto-redirect to dashboard)
    await signOut({ redirect: false });
    
    // Redirect to the secure logout URL provided by the server
    window.location.href = result.logoutUrl || '/auth/signin';
    
  } catch (error) {
    // Fallback: try to clear NextAuth session and redirect to sign-in
    try {
      await signOut({ redirect: false });
    } catch (signOutError) {
      // No logger here, as this is client-side fallback
    }
    
    // Final fallback: redirect to sign-in page
    window.location.href = '/auth/signin';
  }
}
