import * as cron from 'node-cron';
import { createPendingPaymentsForToday } from './payment-processor';
import { runUpdatePaymentStatus} from '../payment-scheduler';
import logger from '@/lib/logger';

class PaymentScheduler {
  private static instance: PaymentScheduler;
  private paymentCreationTask: cron.ScheduledTask | null = null;
  private paymentStatusTask: cron.ScheduledTask | null = null;

  private constructor() {
    // Handle graceful shutdown
    process.on('SIGINT', () => this.handleShutdown('SIGINT'));
    process.on('SIGTERM', () => this.handleShutdown('SIGTERM'));
    process.on('SIGUSR2', () => this.handleShutdown('SIGUSR2')); // For nodemon restarts
  }

  static getInstance(): PaymentScheduler {
    if (!PaymentScheduler.instance) {
      PaymentScheduler.instance = new PaymentScheduler();
    }
    return PaymentScheduler.instance;
  }

  start() {
    // Payment Creation Task - runs at midnight every day
    const paymentCreationCron = process.env.PAYMENT_CREATION_CRON_SCHEDULE || '0 0 * * *'; // Default: midnight every day

    // Payment Status Update Task - runs every hour to check for overdue payments
    const paymentStatusCron = process.env.PAYMENT_STATUS_CRON_SCHEDULE || '0 0 * * *'; // Default: midnight every day

    if (this.paymentCreationTask || this.paymentStatusTask) {
      logger.warn('⚠️  Payment scheduler is already running');
      return;
    }

    // Log timezone and environment info for debugging
    const timezone = process.env.TZ || 'UTC';
    logger.info(`🌍 Using timezone: ${timezone}`);
    logger.info(`📅 Current time: ${new Date().toISOString()}`);
    logger.info(`📅 Starting payment creation scheduler with cron expression: '${paymentCreationCron}'`);
    logger.info(`📅 Starting payment status scheduler with cron expression: '${paymentStatusCron}'`);
    logger.info(`⚙️  Max retries: ${process.env.PAYMENT_SCHEDULER_MAX_RETRIES || '3'}`);
    logger.info(`⏱️  Retry delay: ${(parseInt(process.env.PAYMENT_SCHEDULER_RETRY_DELAY_MS || '1800000') / 1000 / 60)} minutes`);

    try {
      // Validate cron expressions before scheduling
      if (!cron.validate(paymentCreationCron)) {
        throw new Error(`Invalid payment creation cron expression: ${paymentCreationCron}`);
      }
      if (!cron.validate(paymentStatusCron)) {
        throw new Error(`Invalid payment status cron expression: ${paymentStatusCron}`);
      }

      // Payment Creation Task
      this.paymentCreationTask = cron.schedule(paymentCreationCron, async () => {
        logger.info(`🔄 [${new Date().toISOString()}] Running payment creation scheduler...`);

        try {
          await this.processPaymentCreation();
        } catch (error) {
          logger.error('❌ Error in payment creation scheduler:', { error });
        }
      }, {
        timezone: timezone
      });

      // Payment Status Update Task
      this.paymentStatusTask = cron.schedule(paymentStatusCron, async () => {
        logger.info(`🔄 [${new Date().toISOString()}] Running payment status update scheduler...`);

        try {
          await this.processPaymentStatusUpdates();
        } catch (error) {
          logger.error('❌ Error in payment status update scheduler:', { error });
        }
      }, {
        timezone: timezone
      });

      logger.info('✅ Payment schedulers started successfully');
    } catch (error) {
      logger.error('❌ Failed to create cron schedulers:', { error });
      // Clean up any partially created tasks
      this.stop();
      throw error;
    }
  }

  stop() {
    if (this.paymentCreationTask) {
      this.paymentCreationTask.destroy();
      this.paymentCreationTask = null;
      logger.info('🛑 Payment creation scheduler stopped');
    }

    if (this.paymentStatusTask) {
      this.paymentStatusTask.destroy();
      this.paymentStatusTask = null;
      logger.info('🛑 Payment status scheduler stopped');
    }
  }

  private handleShutdown(signal: string) {
    logger.info(`📥 Received ${signal}, shutting down payment scheduler...`);
    this.stop();
  }

  private async processPaymentCreation() {
    let retryCount = 0;
    const maxRetries = process.env.PAYMENT_SCHEDULER_MAX_RETRIES ? parseInt(process.env.PAYMENT_SCHEDULER_MAX_RETRIES) : 3;
    const retryDelayMs = process.env.PAYMENT_SCHEDULER_RETRY_DELAY_MS ? parseInt(process.env.PAYMENT_SCHEDULER_RETRY_DELAY_MS) : 30 * 60 * 1000; // 30 minutes default

    while (retryCount <= maxRetries) {
      try {
        logger.info(`💰 Processing payments (attempt ${retryCount + 1}/${maxRetries + 1})...`);
        const result = await createPendingPaymentsForToday();
        
        logger.info(`✅ Payment processing completed successfully:`, {
          totalAssignments: result.totalAssignments,
          paymentsCreated: result.paymentsCreated,
          errors: result.errors?.length || 0,
          timestamp: new Date().toISOString()
        });
        
        // If successful, break out of retry loop
        break;
        
      } catch (error) {
        retryCount++;
        logger.error(`❌ Payment processing failed (attempt ${retryCount}/${maxRetries + 1}):`, { error });
        
        if (retryCount <= maxRetries) {
          logger.info(`⏳ Retrying in ${retryDelayMs / 1000 / 60} minutes...`);
          await new Promise(resolve => setTimeout(resolve, retryDelayMs));
        } else {
          logger.error('💥 Payment processing failed after all retries');
        }
      }
    }
  }

  private async processPaymentStatusUpdates() {
    let retryCount = 0;
    const maxRetries = process.env.PAYMENT_SCHEDULER_MAX_RETRIES ? parseInt(process.env.PAYMENT_SCHEDULER_MAX_RETRIES) : 3;
    const retryDelayMs = process.env.PAYMENT_SCHEDULER_RETRY_DELAY_MS ? parseInt(process.env.PAYMENT_SCHEDULER_RETRY_DELAY_MS) : 30 * 60 * 1000; // 30 minutes default

    while (retryCount <= maxRetries) {
      try {
        logger.info(`📊 Processing payment status updates (attempt ${retryCount + 1}/${maxRetries + 1})...`);
        const result = await runUpdatePaymentStatus();

        logger.info(`✅ Payment status update completed successfully:`, {
          tenantsProcessed: result.tenantsProcessed,
          paymentsCreated: result.paymentsCreated,
          paymentsMarkedOverdue: result.paymentsMarkedOverdue,
          errors: result.errors?.length || 0,
          timestamp: new Date().toISOString()
        });

        // If successful, break out of retry loop
        break;

      } catch (error) {
        retryCount++;
        logger.error(`❌ Payment status update failed (attempt ${retryCount}/${maxRetries + 1}):`, { error });

        if (retryCount <= maxRetries) {
          logger.info(`⏳ Retrying in ${retryDelayMs / 1000 / 60} minutes...`);
          await new Promise(resolve => setTimeout(resolve, retryDelayMs));
        } else {
          logger.error('💥 Payment status update failed after all retry attempts');
          throw error;
        }
      }
    }

    const result = {
      success: true,
      timestamp: new Date().toISOString(),
      retryCount
    };

    return result;
  }
}

export default PaymentScheduler;
