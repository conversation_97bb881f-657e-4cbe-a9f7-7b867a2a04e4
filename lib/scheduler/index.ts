import PaymentScheduler from './payment-scheduler';
import logger from '@/lib/logger';

let scheduler: PaymentScheduler | null = null;
let isInitialized = false;

export function initializeScheduler() {
  if (isInitialized) {
    logger.warn('⚠️  Payment scheduler already initialized');
    return scheduler;
  }

  scheduler = PaymentScheduler.getInstance();
  scheduler.start();
  isInitialized = true;
  
  logger.info('✅ Payment scheduler initialized and started');
  return scheduler;
}

export function getScheduler(): PaymentScheduler | null {
  return scheduler;
}

export function stopScheduler() {
  if (scheduler) {
    scheduler.stop();
    scheduler = null;
    isInitialized = false;
    logger.info('🛑 Payment scheduler stopped and cleaned up');
  }
}
