// Initialize the payment scheduler when the application starts
import { initializeScheduler } from './index';
import logger from '@/lib/logger';

// Global flag to prevent multiple initializations
let isSchedulerInitialized = false;

// Initialize scheduler function that can be called manually
export function initPaymentScheduler() {
  if (isSchedulerInitialized) {
    logger.info('Payment scheduler already initialized, skipping...');
    return;
  }
  
  // Don't run on client side
  if (typeof window !== 'undefined') {
    logger.info('Skipping payment scheduler initialization on client side');
    return;
  }
  
  logger.info('🚀 Starting payment scheduler initialization...');
  
  try {
    // Set default timezone if not set
    if (!process.env.TZ) {
      process.env.TZ = 'UTC';
      logger.info('🌍 Timezone not set, defaulting to UTC');
    }
    
    initializeScheduler();
    isSchedulerInitialized = true;
    logger.info('✅ Payment scheduler initialized successfully');
  } catch (error) {
    logger.error('❌ Failed to initialize payment scheduler:', { error });
    isSchedulerInitialized = false; // Reset flag on error
    
    // Don't throw the error to prevent app startup failure
    // The scheduler can be retried later or run manually
    logger.warn('⚠️  Application will continue without automatic payment scheduling');
  }
}

export {};
