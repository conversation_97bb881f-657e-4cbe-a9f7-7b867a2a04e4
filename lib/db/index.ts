// Re-export all database classes
export { TenantAwareDBBase } from './base';
export { SchoolsDB } from './schools';
export { BranchesDB } from './branches';
export { InstructorsDB } from './instructors';
export { TeamsDB } from './teams';
export { AthletesDB } from './athletes';
export { PaymentsDB } from './payments';
export { PaymentTransactionsDB } from './payment-transactions';
export { ExpensesDB } from './expenses';
export { FacilitiesDB } from './facilities';
export { ItemsDB } from './items';
export { FinancialDB } from './financial';
export { SmsConfigurationsDB } from './sms-configurations';
export { SmsLogsDB } from './sms-logs';
export { SmsBalanceDB } from './sms-balance';
export { SmsPricingTiersDB } from './sms-pricing-tiers';
export { SmsTemplatesDB } from './sms-templates';

// Import for internal use
import { SchoolsDB } from './schools';
import { BranchesDB } from './branches';
import { InstructorsDB } from './instructors';
import { TeamsDB } from './teams';
import { AthletesDB } from './athletes';
import { PaymentsDB } from './payments';
import { PaymentTransactionsDB } from './payment-transactions';
import { ExpensesDB } from './expenses';
import { FacilitiesDB } from './facilities';
import { ItemsDB } from './items';
import { FinancialDB } from './financial';
import { SmsConfigurationsDB } from './sms-configurations';
import { SmsLogsDB } from './sms-logs';
import { SmsBalanceDB } from './sms-balance';
import { SmsPricingTiersDB } from './sms-pricing-tiers';
import { SmsTemplatesDB } from './sms-templates';

// Main unified class that delegates to individual domain classes
// This maintains compatibility with existing code
export class TenantAwareDB {
  // Schools
  static async getSchools(tenantId?: string) {
    return SchoolsDB.getSchools(tenantId);
  }

  static async getSchoolById(id: string, tenantId?: string) {
    return SchoolsDB.getSchoolById(id, tenantId);
  }

  static async getSchoolByName(name: string, tenantId?: string) {
    return SchoolsDB.getSchoolByName(name, tenantId);
  }

  static async getTeamsBySchoolId(schoolId: string, tenantId?: string) {
    return SchoolsDB.getTeamsBySchoolId(schoolId, tenantId);
  }

  static async createSchool(data: Parameters<typeof SchoolsDB.createSchool>[0], tenantId?: string, userId?: bigint) {
    return SchoolsDB.createSchool(data, tenantId, userId);
  }

  static async updateSchool(id: string, data: Parameters<typeof SchoolsDB.updateSchool>[1], tenantId?: string, userId?: bigint) {
    return SchoolsDB.updateSchool(id, data, tenantId, userId);
  }

  static async deleteSchool(id: string, tenantId?: string) {
    return SchoolsDB.deleteSchool(id, tenantId);
  }

  static async assignBranchesToSchool(schoolId: string, branchIds: string[], tenantId?: string, userId?: bigint) {
    return SchoolsDB.assignBranchesToSchool(schoolId, branchIds, tenantId, userId);
  }

  static async getSchoolBranches(schoolId: string, tenantId?: string) {
    return SchoolsDB.getSchoolBranches(schoolId, tenantId);
  }

  // Branches
  static async getBranches() {
    return BranchesDB.getBranches();
  }

  static async getBranchById(id: string) {
    return BranchesDB.getBranchById(id);
  }

  // Instructors
  static async getInstructors(tenantId?: string) {
    return InstructorsDB.getInstructors(tenantId);
  }

  static async getInstructorsPaginated(tenantId?: string, options?: Parameters<typeof InstructorsDB.getInstructorsPaginated>[1]) {
    return InstructorsDB.getInstructorsPaginated(tenantId, options);
  }

  static async getInstructorById(id: string, tenantId?: string) {
    return InstructorsDB.getInstructorById(id, tenantId);
  }

  static async getInstructorByEmail(email: string, tenantId?: string) {
    return InstructorsDB.getInstructorByEmail(email, tenantId);
  }

  static async getInstructorByNationalId(nationalId: string, tenantId?: string) {
    return InstructorsDB.getInstructorByNationalId(nationalId, tenantId);
  }

  static async getTeamsByInstructorId(instructorId: string, tenantId?: string) {
    return InstructorsDB.getTeamsByInstructorId(instructorId, tenantId);
  }

  static async updateInstructorBranches(instructorId: string, branchIds: string[], tenantId?: string) {
    return InstructorsDB.updateInstructorBranches(instructorId, branchIds, tenantId);
  }

  static async updateInstructorSchools(instructorId: string, schoolIds: string[], tenantId?: string) {
    return InstructorsDB.updateInstructorSchools(instructorId, schoolIds, tenantId);
  }

  static async createInstructor(data: Parameters<typeof InstructorsDB.createInstructor>[0], tenantId?: string, userId?: bigint) {
    return InstructorsDB.createInstructor(data, tenantId, userId);
  }

  static async updateInstructor(id: string, data: Parameters<typeof InstructorsDB.updateInstructor>[1], tenantId?: string, userId?: bigint) {
    return InstructorsDB.updateInstructor(id, data, tenantId, userId);
  }

  static async deleteInstructor(id: string, tenantId?: string) {
    return InstructorsDB.deleteInstructor(id, tenantId);
  }

  // Teams
  static async getTeams(tenantId?: string) {
    return TeamsDB.getTeams(tenantId);
  }

  static async getTeamsPaginated(tenantId?: string, options?: Parameters<typeof TeamsDB.getTeamsPaginated>[1]) {
    return TeamsDB.getTeamsPaginated(tenantId, options);
  }

  static async getTeamById(id: string, tenantId?: string) {
    return TeamsDB.getTeamById(id, tenantId);
  }

  static async getTeamByNameAndSchool(name: string, schoolId: string, tenantId?: string) {
    return TeamsDB.getTeamByNameAndSchool(name, schoolId, tenantId);
  }

  static async createTeam(data: Parameters<typeof TeamsDB.createTeam>[0], tenantId?: string, userId?: bigint) {
    return TeamsDB.createTeam(data, tenantId, userId);
  }

  static async createTeamWithSchedules(teamData: Parameters<typeof TeamsDB.createTeamWithSchedules>[0], scheduleData: Parameters<typeof TeamsDB.createTeamWithSchedules>[1], tenantId?: string, userId?: bigint) {
    return TeamsDB.createTeamWithSchedules(teamData, scheduleData, tenantId, userId);
  }

  static async updateTeam(id: string, data: Parameters<typeof TeamsDB.updateTeam>[1], tenantId?: string, userId?: bigint) {
    return TeamsDB.updateTeam(id, data, tenantId, userId);
  }

  static async deleteTeam(id: string, tenantId?: string) {
    return TeamsDB.deleteTeam(id, tenantId);
  }

  static async deleteTrainingSchedulesByTeamId(teamId: string, tenantId?: string) {
    return TeamsDB.deleteTrainingSchedulesByTeamId(teamId, tenantId);
  }

  static async updateTeamWithSchedules(teamId: string, teamData: Parameters<typeof TeamsDB.updateTeamWithSchedules>[1], scheduleData: Parameters<typeof TeamsDB.updateTeamWithSchedules>[2], tenantId?: string, userId?: bigint) {
    return TeamsDB.updateTeamWithSchedules(teamId, teamData, scheduleData, tenantId, userId);
  }

  static async getTrainingSchedulesByTeamId(teamId: string, tenantId?: string) {
    return TeamsDB.getTrainingSchedulesByTeamId(teamId, tenantId);
  }

  // Athletes
  static async getAthletes(tenantId?: string) {
    return AthletesDB.getAthletes(tenantId);
  }

  static async getAthletesPaginated(tenantId?: string, options?: Parameters<typeof AthletesDB.getAthletesPaginated>[1]) {
    return AthletesDB.getAthletesPaginated(tenantId, options);
  }

  static async getAthleteById(id: string, tenantId?: string) {
    return AthletesDB.getAthleteById(id, tenantId);
  }

  static async getAthleteByNationalId(nationalId:string, tenantId?: string) {
    return AthletesDB.getAthleteByNationalId(nationalId, tenantId);
  }

  static async getAthletesByTeamId(teamId: string, tenantId?: string) {
    return AthletesDB.getAthletesByTeamId(teamId, tenantId);
  }

  static async getAthletesByTeamIdWithContactDetails(teamId: string, tenantId?: string) {
    return AthletesDB.getAthletesByTeamIdWithContactDetails(teamId, tenantId);
  }

  static async getAthletesByIdsWithContactDetails(athleteIds: string[], tenantId?: string) {
    return AthletesDB.getAthletesByIdsWithContactDetails(athleteIds, tenantId);
  }

  static async getAthletesByIds(athleteIds: string[], tenantId?: string) {
    return AthletesDB.getAthletesByIds(athleteIds, tenantId);
  }

  static async getAllAthletesWithContactDetails(tenantId?: string) {
    return AthletesDB.getAllAthletesWithContactDetails(tenantId);
  }

  static async getOverdueAthletes(tenantId?: string) {
    return AthletesDB.getOverdueAthletes(tenantId);
  }

  static async createAthlete(data: Parameters<typeof AthletesDB.createAthlete>[0], tenantId?: string, userId?: bigint) {
    return AthletesDB.createAthlete(data, tenantId, userId);
  }

  static async updateAthlete(id: string, data: Parameters<typeof AthletesDB.updateAthlete>[1], tenantId?: string, userId?: bigint) {
    return AthletesDB.updateAthlete(id, data, tenantId, userId);
  }

  static async deleteAthlete(id: string, tenantId?: string) {
    return AthletesDB.deleteAthlete(id, tenantId);
  }

  static async addAthleteToTeam(athleteId: string, teamId: string, joinedAt?: Date) {
    return AthletesDB.addAthleteToTeam(athleteId, teamId, joinedAt);
  }

  static async removeAthleteFromTeam(athleteId: string, teamId: string) {
    return AthletesDB.removeAthleteFromTeam(athleteId, teamId);
  }

  static async hasAthleteNotPaidPayment(athleteId: string, tenantId?: string) {
    return AthletesDB.hasAthleteNotPaidPayment(athleteId, tenantId);
  }

  // Payments
  static async getPayments(tenantId?: string) {
    return PaymentsDB.getPayments(tenantId);
  }

  static async getPaymentsPaginated(
    page: number = 1,
    limit: number = 10,
    search?: string,
    sortBy: string = 'date',
    sortOrder: 'asc' | 'desc' = 'desc',
    filters: Record<string, string> = {},
    tenantId?: string
  ) {
    return PaymentsDB.getPaymentsPaginated(page, limit, search, sortBy, sortOrder, filters, tenantId);
  }

  static async getPaymentById(id: string, tenantId?: string) {
    return PaymentsDB.getPaymentById(id, tenantId);
  }

  static async getPaymentPlans(tenantId?: string) {
    return PaymentsDB.getPaymentPlans(tenantId);
  }

  static async getPaymentPlanById(id: string, tenantId?: string) {
    return PaymentsDB.getPaymentPlanById(id, tenantId);
  }

  static async getPaymentPlanByName(name: string, tenantId?: string) {
    return PaymentsDB.getPaymentPlanByName(name, tenantId);
  }

  static async checkPaymentPlanHasActiveAssignments(planId: string, tenantId?: string) {
    return PaymentsDB.checkPaymentPlanHasActiveAssignments(planId, tenantId);
  }

  static async getPaymentsByPlanId(planId: string, tenantId?: string) {
    return PaymentsDB.getPaymentsByPlanId(planId, tenantId);
  }

  static async getPaymentPlanStats(planId: string, tenantId?: string) {
    return PaymentsDB.getPaymentPlanStats(planId, tenantId);
  }

  static async getCompletedPayments(tenantId?: string) {
    return PaymentsDB.getCompletedPayments(tenantId);
  }

  static async getOverduePayments(tenantId?: string) {
    return PaymentsDB.getOverduePayments(tenantId);
  }

  static async getPendingPaymentsWithAthleteDetails(tenantId?: string) {
    return PaymentsDB.getPendingPaymentsWithAthleteDetails(tenantId);
  }

  static async getOverduePaymentsWithAthleteDetails(tenantId?: string) {
    return PaymentsDB.getOverduePaymentsWithAthleteDetails(tenantId);
  }

  static async getPartiallyPaidPaymentsWithAthleteDetails(tenantId?: string) {
    return PaymentsDB.getPartiallyPaidPaymentsWithAthleteDetails(tenantId);
  }

  static async getPaymentsByIdsWithAthleteDetails(paymentIds: string[], tenantId?: string) {
    return PaymentsDB.getPaymentsByIdsWithAthleteDetails(paymentIds, tenantId);
  }

  static async createPaymentPlan(data: Parameters<typeof PaymentsDB.createPaymentPlan>[0], tenantId?: string, userId?: bigint) {
    return PaymentsDB.createPaymentPlan(data, tenantId, userId);
  }

  static async updatePaymentPlan(id: string, data: Parameters<typeof PaymentsDB.updatePaymentPlan>[1], tenantId?: string, userId?: bigint) {
    return PaymentsDB.updatePaymentPlan(id, data, tenantId, userId);
  }

  static async updatePaymentPlanBranches(planId: string, branchIds: string[], tenantId?: string) {
    return PaymentsDB.updatePaymentPlanBranches(planId, branchIds, tenantId);
  }

  static async createPayment(data: Parameters<typeof PaymentsDB.createPayment>[0], tenantId?: string, userId?: bigint) {
    return PaymentsDB.createPayment(data, tenantId, userId);
  }

  static async updatePayment(id: string, data: Parameters<typeof PaymentsDB.updatePayment>[1], tenantId?: string, userId?: bigint) {
    return PaymentsDB.updatePayment(id, data, tenantId, userId);
  }

  static async deletePayment(id: string, tenantId?: string) {
    return PaymentsDB.deletePayment(id, tenantId);
  }

  static async getAthleteOutstandingPaymentsSum(athleteId: string, tenantId?: string, excludePaymentId?: string) {
    return PaymentsDB.getAthleteOutstandingPaymentsSum(athleteId, tenantId, excludePaymentId);
  }

  static async getPaymentPlanAssignmentCount(planId: string, tenantId?: string) {
    return PaymentsDB.getPaymentPlanAssignmentCount(planId, tenantId);
  }

  static async getAthletePaymentBreakdown(athleteId: string, tenantId?: string) {
    return PaymentsDB.getAthletePaymentBreakdown(athleteId, tenantId);
  }

  static async deletePaymentPlan(id: string, tenantId?: string) {
    return PaymentsDB.deletePaymentPlan(id, tenantId);
  }

  // Payment Transactions
  static async getPaymentTransactions(tenantId?: string) {
    return PaymentTransactionsDB.getPaymentTransactions(tenantId);
  }

  static async getPaymentTransactionsPaginated(
    page: number = 1,
    limit: number = 10,
    search?: string,
    sortBy: string = 'transactionDate',
    sortOrder: 'asc' | 'desc' = 'desc',
    filters: Record<string, string> = {},
    tenantId?: string
  ) {
    return PaymentTransactionsDB.getPaymentTransactionsPaginated(page, limit, search, sortBy, sortOrder, filters, tenantId);
  }

  static async getPaymentTransactionById(id: string, tenantId?: string) {
    return PaymentTransactionsDB.getPaymentTransactionById(id, tenantId);
  }

  static async getTransactionsByPaymentId(paymentId: string, tenantId?: string) {
    return PaymentTransactionsDB.getTransactionsByPaymentId(paymentId, tenantId);
  }

  static async getTransactionsByAthleteId(athleteId: string, tenantId?: string) {
    return PaymentTransactionsDB.getTransactionsByAthleteId(athleteId, tenantId);
  }

  static async createPaymentTransaction(data: Parameters<typeof PaymentTransactionsDB.createPaymentTransaction>[0], tenantId?: string, userId?: bigint) {
    return PaymentTransactionsDB.createPaymentTransaction(data, tenantId, userId);
  }

  static async updatePaymentTransaction(id: string, data: Parameters<typeof PaymentTransactionsDB.updatePaymentTransaction>[1], tenantId?: string, userId?: bigint) {
    return PaymentTransactionsDB.updatePaymentTransaction(id, data, tenantId, userId);
  }

  static async deletePaymentTransaction(id: string, tenantId?: string) {
    return PaymentTransactionsDB.deletePaymentTransaction(id, tenantId);
  }

  static async getAthleteBalanceTopUpsSum(athleteId: string, tenantId?: string) {
    return PaymentTransactionsDB.getAthleteBalanceTopUpsSum(athleteId, tenantId);
  }

  static async getAthleteBalanceUsageSum(athleteId: string, tenantId?: string) {
    return PaymentTransactionsDB.getAthleteBalanceUsageSum(athleteId, tenantId);
  }

  // Expenses
  static async getExpenses(tenantId?: string) {
    return ExpensesDB.getExpenses(tenantId);
  }

  static async getExpensesPaginated(options: Parameters<typeof ExpensesDB.getExpensesPaginated>[0], tenantId?: string) {
    return ExpensesDB.getExpensesPaginated(options, tenantId);
  }

  static async getExpenseById(id: string, tenantId?: string) {
    return ExpensesDB.getExpenseById(id, tenantId);
  }

  static async createExpense(data: Parameters<typeof ExpensesDB.createExpense>[0], tenantId?: string, userId?: bigint) {
    return ExpensesDB.createExpense(data, tenantId, userId);
  }

  static async updateExpense(id: string, data: Parameters<typeof ExpensesDB.updateExpense>[1], tenantId?: string, userId?: bigint) {
    return ExpensesDB.updateExpense(id, data, tenantId, userId);
  }

  static async deleteExpense(id: string, tenantId?: string) {
    return ExpensesDB.deleteExpense(id, tenantId);
  }

  static async getExpensesByCategory(category: string, tenantId?: string) {
    return ExpensesDB.getExpensesByCategory(category, tenantId);
  }

  static async getExpensesByDateRange(startDate: string | Date, endDate: string | Date, tenantId?: string) {
    return ExpensesDB.getExpensesByDateRange(startDate, endDate, tenantId);
  }

  // Facilities
  static async getFacilities(tenantId?: string) {
    return FacilitiesDB.getFacilities(tenantId);
  }

  static async getFacilityById(id: string, tenantId?: string) {
    return FacilitiesDB.getFacilityById(id, tenantId);
  }

  static async getFacilityByName(name: string, tenantId?: string) {
    return FacilitiesDB.getFacilityByName(name, tenantId);
  }

  static async getTrainingSchedulesByFacilityId(facilityId: string, tenantId?: string) {
    return FacilitiesDB.getTrainingSchedulesByFacilityId(facilityId, tenantId);
  }

  static async getFacilitySchedules(facilityId: string, tenantId?: string) {
    return FacilitiesDB.getFacilitySchedules(facilityId, tenantId);
  }

  static async getAllFacilitySchedules(tenantId?: string) {
    return FacilitiesDB.getAllFacilitySchedules(tenantId);
  }

  static async createFacility(data: Parameters<typeof FacilitiesDB.createFacility>[0], tenantId?: string, userId?: bigint) {
    return FacilitiesDB.createFacility(data, tenantId, userId);
  }

  static async updateFacility(id: string, data: Parameters<typeof FacilitiesDB.updateFacility>[1], tenantId?: string, userId?: bigint) {
    return FacilitiesDB.updateFacility(id, data, tenantId, userId);
  }

  static async deleteFacility(id: string, tenantId?: string) {
    return FacilitiesDB.deleteFacility(id, tenantId);
  }

  // Items
  static async getItems(tenantId?: string) {
    return ItemsDB.getItems(tenantId);
  }

  static async getItemById(id: string, tenantId?: string) {
    return ItemsDB.getItemById(id, tenantId);
  }

  static async getItemByName(name: string, tenantId?: string) {
    return ItemsDB.getItemByName(name, tenantId);
  }

  static async createItem(data: Parameters<typeof ItemsDB.createItem>[0], tenantId?: string, userId?: bigint) {
    return ItemsDB.createItem(data, tenantId, userId);
  }

  static async updateItem(id: string, data: Parameters<typeof ItemsDB.updateItem>[1], tenantId?: string, userId?: bigint) {
    return ItemsDB.updateItem(id, data, tenantId, userId);
  }

  static async deleteItem(id: string, tenantId?: string) {
    return ItemsDB.deleteItem(id, tenantId);
  }

  // Item Purchases
  static async createItemPurchase(data: Parameters<typeof ItemsDB.createItemPurchase>[0], tenantId?: string, userId?: bigint) {
    return ItemsDB.createItemPurchase(data, tenantId, userId);
  }

  static async getItemPurchases(tenantId?: string) {
    return ItemsDB.getItemPurchases(tenantId);
  }

  static async getItemPurchaseById(id: string, tenantId?: string) {
    return ItemsDB.getItemPurchaseById(id, tenantId);
  }

  static async updateItemPurchase(id: string, data: Parameters<typeof ItemsDB.updateItemPurchase>[1], tenantId?: string, userId?: bigint) {
    return ItemsDB.updateItemPurchase(id, data, tenantId, userId);
  }

  static async updateItemStock(itemId: string, quantityChange: number, tenantId?: string, userId?: bigint) {
    return ItemsDB.updateItemStock(itemId, quantityChange, tenantId, userId);
  }

  static async getPendingPurchasesByItemId(itemId: string, tenantId?: string) {
    return ItemsDB.getPendingPurchasesByItemId(itemId, tenantId);
  }

  static async getItemsByCategory(category: string, tenantId?: string) {
    return ItemsDB.getItemsByCategory(category, tenantId);
  }

  static async getLowStockItems(threshold: number, tenantId?: string) {
    return ItemsDB.getLowStockItems(threshold, tenantId);
  }

  // Financial
  static async getFinancialSummary(tenantId?: string) {
    return FinancialDB.getFinancialSummary(tenantId);
  }

  // SMS Configurations
  static async getSmsConfigurations(tenantId?: string) {
    return SmsConfigurationsDB.getSmsConfigurations(tenantId);
  }

  static async getActiveSmsConfiguration(tenantId?: string) {
    return SmsConfigurationsDB.getActiveSmsConfiguration(tenantId);
  }

  static async getSmsConfigurationById(id: string, tenantId?: string) {
    return SmsConfigurationsDB.getSmsConfigurationById(id, tenantId);
  }

  static async getSmsConfigurationByVersion(version: number, tenantId?: string) {
    return SmsConfigurationsDB.getSmsConfigurationByVersion(version, tenantId);
  }

  static async createSmsConfiguration(data: Parameters<typeof SmsConfigurationsDB.createSmsConfiguration>[0], tenantId?: string, userId?: bigint) {
    return SmsConfigurationsDB.createSmsConfiguration(data, tenantId, userId);
  }

  static async updateSmsConfiguration(id: string, data: Parameters<typeof SmsConfigurationsDB.updateSmsConfiguration>[1], tenantId?: string, userId?: bigint) {
    return SmsConfigurationsDB.updateSmsConfiguration(id, data, tenantId, userId);
  }

  static async deleteSmsConfiguration(id: string, tenantId?: string) {
    return SmsConfigurationsDB.deleteSmsConfiguration(id, tenantId);
  }

  static async activateSmsConfiguration(id: string, tenantId?: string, userId?: bigint) {
    return SmsConfigurationsDB.activateConfiguration(id, tenantId, userId);
  }

  static async deactivateAllConfigurations(tenantId?: string, userId?: bigint) {
    return SmsConfigurationsDB.deactivateAllConfigurations(tenantId, userId);
  }

  // SMS Pricing Tiers methods
  static async getActivePricingTiers() {
    return SmsPricingTiersDB.getActivePricingTiers();
  }

  static async getAllPricingTiers() {
    return SmsPricingTiersDB.getAllPricingTiers();
  }

  static async createPricingTier(data: any, userId?: bigint) {
    return SmsPricingTiersDB.createPricingTier(data, userId);
  }

  static async initializeDefaultPricingTiers(userId?: bigint) {
    return SmsPricingTiersDB.initializeDefaultPricingTiers(userId);
  }

  // SMS Logs
  static async getSmsLogs(tenantId?: string) {
    return SmsLogsDB.getSmsLogs(tenantId);
  }

  static async getSmsLogsPaginated(tenantId?: string, options?: Parameters<typeof SmsLogsDB.getSmsLogsPaginated>[1]) {
    return SmsLogsDB.getSmsLogsPaginated(tenantId, options);
  }

  static async getSmsLogById(id: string, tenantId?: string) {
    return SmsLogsDB.getSmsLogById(id, tenantId);
  }

  static async createSmsLog(data: Parameters<typeof SmsLogsDB.createSmsLog>[0], tenantId?: string) {
    return SmsLogsDB.createSmsLog(data, tenantId);
  }

  static async updateSmsLog(id: string, data: Parameters<typeof SmsLogsDB.updateSmsLog>[1], tenantId?: string) {
    return SmsLogsDB.updateSmsLog(id, data, tenantId);
  }



  static async getSmsLogsByAthleteId(athleteId: string, tenantId?: string) {
    return SmsLogsDB.getSmsLogsByAthleteId(athleteId, tenantId);
  }

  static async getSmsLogsByTeamId(teamId: string, tenantId?: string) {
    return SmsLogsDB.getSmsLogsByTeamId(teamId, tenantId);
  }

  static async getSmsStats(tenantId?: string, dateFrom?: Date, dateTo?: Date) {
    return SmsLogsDB.getSmsStats(tenantId, dateFrom, dateTo);
  }

  static async createSmsLogPaymentRelation(smsLogId: string, paymentId: string, tenantId?: string) {
    return SmsLogsDB.createSmsLogPaymentRelation(smsLogId, paymentId, tenantId);
  }

  // SMS Balance
  static async getSmsBalance(tenantId?: string) {
    return SmsBalanceDB.getSmsBalance(tenantId);
  }

  static async createSmsBalance(data: Parameters<typeof SmsBalanceDB.createSmsBalance>[0], tenantId?: string, userId?: bigint) {
    return SmsBalanceDB.createSmsBalance(data, tenantId, userId);
  }

  static async updateSmsBalance(balance: number, tenantId?: string, userId?: bigint) {
    return SmsBalanceDB.updateSmsBalance(balance, tenantId, userId);
  }

  static async decrementSmsBalance(amount: number, tenantId?: string, userId?: bigint) {
    return SmsBalanceDB.decrementSmsBalance(amount, tenantId, userId);
  }

  static async incrementSmsBalance(amount: number, tenantId?: string, userId?: bigint) {
    return SmsBalanceDB.incrementSmsBalance(amount, tenantId, userId);
  }

  static async initializeSmsBalance(tenantId?: string, userId?: bigint) {
    return SmsBalanceDB.initializeSmsBalance(tenantId, userId);
  }

  static async checkSufficientSmsBalance(requiredAmount: number, tenantId?: string) {
    return SmsBalanceDB.checkSufficientBalance(requiredAmount, tenantId);
  }

  // SMS Templates (Global - not tenant-specific)
  static async getSmsTemplates(language?: string) {
    return SmsTemplatesDB.getSmsTemplates(language);
  }

  static async getActiveSmsTemplates(language?: string) {
    return SmsTemplatesDB.getActiveSmsTemplates(language);
  }

  static async getSmsTemplateByType(
    type: 'general' | 'event' | 'meeting' | 'holiday' | 'custom',
    language: string = 'en'
  ) {
    return SmsTemplatesDB.getSmsTemplateByType(type, language);
  }

  static async createSmsTemplate(
    data: Parameters<typeof SmsTemplatesDB.createSmsTemplate>[0]
  ) {
    return SmsTemplatesDB.createSmsTemplate(data);
  }

  static async updateSmsTemplate(
    id: string,
    data: Parameters<typeof SmsTemplatesDB.updateSmsTemplate>[1]
  ) {
    return SmsTemplatesDB.updateSmsTemplate(id, data);
  }

  static async deleteSmsTemplate(id: string) {
    return SmsTemplatesDB.deleteSmsTemplate(id);
  }

  static async deactivateOtherSmsTemplates(
    type: 'general' | 'event' | 'meeting' | 'holiday' | 'custom',
    language: string,
    excludeId?: string
  ) {
    return SmsTemplatesDB.deactivateOtherTemplates(type, language, excludeId);
  }
}

// Default export for easy importing
export default TenantAwareDB;
