import { eq, and, desc, asc, sql, count, sum, ilike, or } from 'drizzle-orm';
import { db } from '@/src/db';
import * as schema from '@/src/db/schema';
import { TenantAwareDBBase } from './base';

export class PaymentTransactionsDB extends TenantAwareDBBase {
  
  static async getPaymentTransactions(tenantId?: string) {
    const filter = await this.getTenantFilter(tenantId);
    return db.select({
      id: schema.paymentTransactions.id,
      tenantId: schema.paymentTransactions.tenantId,
      athleteId: schema.paymentTransactions.athleteId,
      paymentId: schema.paymentTransactions.paymentId,
      amount: schema.paymentTransactions.amount,
      transactionMethod: schema.paymentTransactions.transactionMethod,
      transactionDate: schema.paymentTransactions.transactionDate,
      notes: schema.paymentTransactions.notes,
      createdAt: schema.paymentTransactions.createdAt,
      updatedAt: schema.paymentTransactions.updatedAt,
      createdBy: schema.paymentTransactions.createdBy,
      updatedBy: schema.paymentTransactions.updatedBy,
      athlete: {
        id: schema.athletes.id,
        name: schema.athletes.name,
        surname: schema.athletes.surname,
        parentEmail: schema.athletes.parentEmail,
        parentPhone: schema.athletes.parentPhone,
      },
      payment: {
        id: schema.payments.id,
        amount: schema.payments.amount,
        description: schema.payments.description,
        type: schema.payments.type,
      }
    })
    .from(schema.paymentTransactions)
    .leftJoin(schema.athletes, eq(schema.paymentTransactions.athleteId, schema.athletes.id))
    .leftJoin(schema.payments, eq(schema.paymentTransactions.paymentId, schema.payments.id))
    .where(eq(schema.paymentTransactions.tenantId, filter.tenantId))
    .orderBy(desc(schema.paymentTransactions.transactionDate));
  }

  static async getPaymentTransactionsPaginated(
    page: number = 1,
    limit: number = 10,
    search?: string,
    sortBy: string = 'transactionDate',
    sortOrder: 'asc' | 'desc' = 'desc',
    filters: Record<string, string> = {},
    tenantId?: string
  ) {
    const filter = await this.getTenantFilter(tenantId);
    const offset = (page - 1) * limit;

    // Build WHERE conditions
    const conditions = [eq(schema.paymentTransactions.tenantId, filter.tenantId)];

    // Add search conditions
    if (search && search.trim()) {
      const searchTerms = search.trim().toLowerCase().split(/\s+/);
      
      if (searchTerms.length === 1) {
        // Single term search
        const searchTerm = `%${searchTerms[0]}%`;
        conditions.push(
          or(
            ilike(schema.athletes.name, searchTerm),
            ilike(schema.athletes.surname, searchTerm),
            ilike(schema.paymentTransactions.notes, searchTerm),
            ilike(sql`CAST(${schema.paymentTransactions.amount} AS TEXT)`, searchTerm)
          )!
        );
      } else {
        // Multi-term search - each term must match somewhere
        const termConditions = searchTerms.map(term => {
          const searchTerm = `%${term}%`;
          return or(
            ilike(schema.athletes.name, searchTerm),
            ilike(schema.athletes.surname, searchTerm),
            ilike(schema.paymentTransactions.notes, searchTerm),
            ilike(sql`CAST(${schema.paymentTransactions.amount} AS TEXT)`, searchTerm),
            ilike(sql`CONCAT(${schema.athletes.name}, ' ', ${schema.athletes.surname})`, searchTerm)
          )!;
        });
        
        conditions.push(and(...termConditions)!);
      }
    }

    // Add filter conditions
    if (filters.transactionMethod) {
      conditions.push(eq(schema.paymentTransactions.transactionMethod, filters.transactionMethod as any));
    }

    if (filters.athleteId) {
      conditions.push(eq(schema.paymentTransactions.athleteId, filters.athleteId));
    }

    if (filters.paymentId) {
      if (filters.paymentId === 'null') {
        conditions.push(sql`${schema.paymentTransactions.paymentId} IS NULL`);
      } else {
        conditions.push(eq(schema.paymentTransactions.paymentId, filters.paymentId));
      }
    }

    if (filters.fromDate) {
      conditions.push(sql`${schema.paymentTransactions.transactionDate} >= ${filters.fromDate}`);
    }

    if (filters.toDate) {
      conditions.push(sql`${schema.paymentTransactions.transactionDate} <= ${filters.toDate}`);
    }

    // Build ORDER BY
    const orderByColumn = sortBy === 'transactionDate' ? schema.paymentTransactions.transactionDate :
                         sortBy === 'amount' ? schema.paymentTransactions.amount :
                         sortBy === 'transactionMethod' ? schema.paymentTransactions.transactionMethod :
                         schema.paymentTransactions.transactionDate;
    
    const orderByDirection = sortOrder === 'asc' ? asc(orderByColumn) : desc(orderByColumn);

    // Get total count
    const totalResult = await db.select({ count: count() })
      .from(schema.paymentTransactions)
      .leftJoin(schema.athletes, eq(schema.paymentTransactions.athleteId, schema.athletes.id))
      .where(and(...conditions));
    
    const total = totalResult[0]?.count || 0;
    
    const transactions = await db.select({
      id: schema.paymentTransactions.id,
      tenantId: schema.paymentTransactions.tenantId,
      athleteId: schema.paymentTransactions.athleteId,
      paymentId: schema.paymentTransactions.paymentId,
      amount: schema.paymentTransactions.amount,
      transactionMethod: schema.paymentTransactions.transactionMethod,
      transactionDate: schema.paymentTransactions.transactionDate,
      notes: schema.paymentTransactions.notes,
      createdAt: schema.paymentTransactions.createdAt,
      updatedAt: schema.paymentTransactions.updatedAt,
      createdBy: schema.paymentTransactions.createdBy,
      updatedBy: schema.paymentTransactions.updatedBy,
      athlete: {
        id: schema.athletes.id,
        name: schema.athletes.name,
        surname: schema.athletes.surname,
        parentEmail: schema.athletes.parentEmail,
        parentPhone: schema.athletes.parentPhone,
      },
      payment: {
        id: schema.payments.id,
        amount: schema.payments.amount,
        description: schema.payments.description,
        type: schema.payments.type,
      }
    })
    .from(schema.paymentTransactions)
    .leftJoin(schema.athletes, eq(schema.paymentTransactions.athleteId, schema.athletes.id))
    .leftJoin(schema.payments, eq(schema.paymentTransactions.paymentId, schema.payments.id))
    .where(and(...conditions))
    .orderBy(orderByDirection)
    .limit(limit)
    .offset(offset);

    const totalPages = Math.ceil(total / limit);
    const hasNextPage = page < totalPages;
    const hasPreviousPage = page > 1;

    return {
      data: transactions,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNextPage,
        hasPreviousPage,
      },
    };
  }

  static async getPaymentTransactionById(id: string, tenantId?: string) {
    const filter = await this.getTenantFilter(tenantId);
    const result = await db.select({
      id: schema.paymentTransactions.id,
      tenantId: schema.paymentTransactions.tenantId,
      athleteId: schema.paymentTransactions.athleteId,
      paymentId: schema.paymentTransactions.paymentId,
      amount: schema.paymentTransactions.amount,
      transactionMethod: schema.paymentTransactions.transactionMethod,
      transactionDate: schema.paymentTransactions.transactionDate,
      notes: schema.paymentTransactions.notes,
      createdAt: schema.paymentTransactions.createdAt,
      updatedAt: schema.paymentTransactions.updatedAt,
      createdBy: schema.paymentTransactions.createdBy,
      updatedBy: schema.paymentTransactions.updatedBy,
      athlete: {
        id: schema.athletes.id,
        name: schema.athletes.name,
        surname: schema.athletes.surname,
        parentName: schema.athletes.parentName,
        parentEmail: schema.athletes.parentEmail,
        parentPhone: schema.athletes.parentPhone,
      },
      payment: {
        id: schema.payments.id,
        amount: schema.payments.amount,
        description: schema.payments.description,
        type: schema.payments.type,
      }
    })
    .from(schema.paymentTransactions)
    .leftJoin(schema.athletes, eq(schema.paymentTransactions.athleteId, schema.athletes.id))
    .leftJoin(schema.payments, eq(schema.paymentTransactions.paymentId, schema.payments.id))
    .where(and(
      eq(schema.paymentTransactions.id, id),
      eq(schema.paymentTransactions.tenantId, filter.tenantId)
    ));

    return result[0] || null;
  }

  static async getTransactionsByPaymentId(paymentId: string, tenantId?: string) {
    const filter = await this.getTenantFilter(tenantId);
    return db.select({
      id: schema.paymentTransactions.id,
      amount: schema.paymentTransactions.amount,
      transactionMethod: schema.paymentTransactions.transactionMethod,
      transactionDate: schema.paymentTransactions.transactionDate,
      notes: schema.paymentTransactions.notes,
      createdAt: schema.paymentTransactions.createdAt,
    })
    .from(schema.paymentTransactions)
    .where(and(
      eq(schema.paymentTransactions.paymentId, paymentId),
      eq(schema.paymentTransactions.tenantId, filter.tenantId)
    ))
    .orderBy(desc(schema.paymentTransactions.transactionDate));
  }

  static async getTransactionsByAthleteId(athleteId: string, tenantId?: string) {
    const filter = await this.getTenantFilter(tenantId);
    return db.select({
      id: schema.paymentTransactions.id,
      paymentId: schema.paymentTransactions.paymentId,
      amount: schema.paymentTransactions.amount,
      transactionMethod: schema.paymentTransactions.transactionMethod,
      transactionDate: schema.paymentTransactions.transactionDate,
      notes: schema.paymentTransactions.notes,
      payment: {
        id: schema.payments.id,
        amount: schema.payments.amount,
        description: schema.payments.description,
        type: schema.payments.type,
      }
    })
    .from(schema.paymentTransactions)
    .leftJoin(schema.payments, eq(schema.paymentTransactions.paymentId, schema.payments.id))
    .where(and(
      eq(schema.paymentTransactions.athleteId, athleteId),
      eq(schema.paymentTransactions.tenantId, filter.tenantId)
    ))
    .orderBy(desc(schema.paymentTransactions.transactionDate));
  }

  static async createPaymentTransaction(
    data: Omit<typeof schema.paymentTransactions.$inferInsert, 'tenantId' | 'createdBy' | 'updatedBy' | 'createdAt' | 'updatedAt'>, 
    tenantId?: string, 
    userId?: bigint
  ) {
    return this.insertWithAudit(schema.paymentTransactions, data, tenantId, userId);
  }

  static async updatePaymentTransaction(
    id: string, 
    data: Partial<typeof schema.paymentTransactions.$inferInsert>, 
    tenantId?: string, 
    userId?: bigint
  ) {
    return this.updateWithAudit(schema.paymentTransactions, id, data, tenantId, userId);
  }

  static async deletePaymentTransaction(id: string, tenantId?: string) {
    const filter = await this.getTenantFilter(tenantId);
    return db.delete(schema.paymentTransactions)
      .where(and(
        eq(schema.paymentTransactions.id, id),
        eq(schema.paymentTransactions.tenantId, filter.tenantId)
      ))
      .returning();
  }

  /**
   * Get sum of balance top-up transactions for an athlete (paymentId is null)
   */
  static async getAthleteBalanceTopUpsSum(athleteId: string, tenantId?: string): Promise<number> {
    const filter = await this.getTenantFilter(tenantId);
    
    const result = await db
      .select({
        totalAmount: sql`SUM(CAST(${schema.paymentTransactions.amount} AS DECIMAL(10,2)))`,
      })
      .from(schema.paymentTransactions)
      .where(
        and(
          eq(schema.paymentTransactions.athleteId, athleteId),
          eq(schema.paymentTransactions.tenantId, filter.tenantId),
          sql`${schema.paymentTransactions.paymentId} IS NULL`
        )
      );

    return Number(result[0]?.totalAmount || 0);
  }

  /**
   * Get sum of balance usage transactions for an athlete (existing_balance method)
   */
  static async getAthleteBalanceUsageSum(athleteId: string, tenantId?: string): Promise<number> {
    const filter = await this.getTenantFilter(tenantId);
    
    const result = await db
      .select({
        totalAmount: sql`SUM(CAST(${schema.paymentTransactions.amount} AS DECIMAL(10,2)))`,
      })
      .from(schema.paymentTransactions)
      .where(
        and(
          eq(schema.paymentTransactions.athleteId, athleteId),
          eq(schema.paymentTransactions.tenantId, filter.tenantId),
          eq(schema.paymentTransactions.transactionMethod, 'existing_balance')
        )
      );

    return Number(result[0]?.totalAmount || 0);
  }
}
