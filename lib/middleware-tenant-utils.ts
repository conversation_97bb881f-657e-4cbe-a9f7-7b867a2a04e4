import { getToken } from 'next-auth/jwt';
import { NextRequest } from 'next/server';
import { JWT } from 'next-auth/jwt';
import logger from '@/lib/logger';

// ========================================
// MIDDLEWARE-SPECIFIC UTILITIES
// (Edge Runtime Compatible)
// ========================================

/**
 * Extract tenant ID from NextAuth session (for middleware use)
 * @param session - The NextAuth session
 * @returns The tenant ID or null if not found
 */
export function extractTenantIdFromSession(session: any): string | null {
  if (!session) return null;
  
  // Check if tenant ID is already stored in session
  if (session.tenantId) {
    return session.tenantId;
  }
  
  return null;
}

/**
 * Extract user ID from NextAuth session (for middleware use)
 * @param session - The NextAuth session
 * @returns The user ID as bigint or null if not found
 */
export function extractUserIdFromSession(session: any): bigint | null {
  if (!session) return null;
  
  // Check if user ID is already stored in session
  if (session.userId) {
    try {
      return BigInt(session.userId);
    } catch (error) {
      logger.error('Error converting session user ID to bigint:', { error });
    }
  }
  
  return null;
}

/**
 * Extract tenant ID (organization ID) from Zitadel JWT token
 * @param token - The JWT token from NextAuth
 * @returns The organization ID or null if not found
 */
export function extractTenantId(token: JWT | null): string | null {
  if (!token) return null;
  
  // Check in the ID token for roles structure
  if (token.idToken && typeof token.idToken === 'string') {
    try {
      // Decode the ID token to extract organization information from roles
      const payload = JSON.parse(atob(token.idToken.split('.')[1]));
      
      // Check for roles in the expected structure:
      // payload["urn:zitadel:iam:org:project:roles"] = {
      //   "DEFAULT_ROLE": {
      //     "321151237678497795": "zitadel.auth.sitedenemlak.com"
      //   }
      // }
      const roles = payload['urn:zitadel:iam:org:project:roles'];
      if (roles && typeof roles === 'object') {
        // Extract tenant IDs from all roles
        const tenantIds = new Set<string>();
        
        for (const roleName in roles) {
          const roleData = roles[roleName];
          if (roleData && typeof roleData === 'object') {
            // Each key in roleData is a tenant ID
            for (const tenantId in roleData) {
              tenantIds.add(tenantId);
            }
          }
        }
        
        // For now, return the first tenant ID (as mentioned, single tenant for now)
        if (tenantIds.size > 0) {
          return Array.from(tenantIds)[0];
        }
      }
      
      // Fallback: Check for other common organization ID claims
      if (payload.org) {
        return payload.org;
      }
      if (payload.organization_id) {
        return payload.organization_id;
      }
      if (payload['urn:zitadel:iam:org:id']) {
        return payload['urn:zitadel:iam:org:id'];
      }
    } catch (error) {
      logger.error('Error decoding ID token:', { error });
    }
  }
  
  // Fallback options for other token structures
  
  // Option 1: Organization ID in custom claims (direct from token)
  if (token.organization_id) {
    return token.organization_id as string;
  }
  
  // Option 2: Organization ID in 'org' claim
  if (token.org) {
    return token.org as string;
  }
  
  // Option 3: Check for roles in the main token structure
  const roles = (token as any)['urn:zitadel:iam:org:project:roles'];
  if (roles && typeof roles === 'object') {
    const tenantIds = new Set<string>();
    
    for (const roleName in roles) {
      const roleData = roles[roleName];
      if (roleData && typeof roleData === 'object') {
        for (const tenantId in roleData) {
          tenantIds.add(tenantId);
        }
      }
    }
    
    if (tenantIds.size > 0) {
      return Array.from(tenantIds)[0];
    }
  }
  
  // Option 4: Organization ID in nested structure
  if (token.urn && typeof token.urn === 'object') {
    const urn = token.urn as any;
    if (urn.zitadel && urn.zitadel.org) {
      return urn.zitadel.org.id;
    }
  }
  
  return null;
}

/**
 * Extract user ID from Zitadel JWT token
 * @param token - The JWT token from NextAuth
 * @returns The Zitadel user ID as bigint or null if not found
 */
export function extractUserId(token: JWT | null): bigint | null {
  if (!token) return null;
  
  // Zitadel user ID is typically in the 'sub' claim
  if (token.sub) {
    try {
      return BigInt(token.sub);
    } catch (error) {
      logger.error('Error converting user ID to bigint:', { error });
    }
  }
  
  // Alternative locations for user ID in Zitadel tokens
  if (token.user_id) {
    try {
      return BigInt(token.user_id as string);
    } catch (error) {
      logger.error('Error converting user_id to bigint:', { error });
    }
  }
  
  // Check in nested structure
  if (token.urn && typeof token.urn === 'object') {
    const urn = token.urn as any;
    if (urn.zitadel && urn.zitadel.user && urn.zitadel.user.id) {
      try {
        return BigInt(urn.zitadel.user.id);
      } catch (error) {
        logger.error('Error converting nested user ID to bigint:', { error });
      }
    }
  }
  
  // Check in the ID token if available
  if (token.idToken && typeof token.idToken === 'string') {
    try {
      const payload = JSON.parse(atob(token.idToken.split('.')[1]));
      if (payload.sub) {
        return BigInt(payload.sub);
      }
      if (payload.user_id) {
        return BigInt(payload.user_id);
      }
      if (payload['urn:zitadel:iam:user:id']) {
        return BigInt(payload['urn:zitadel:iam:user:id']);
      }
    } catch (error) {
      logger.error('Error decoding ID token for user ID:', { error });
    }
  }
  
  return null;
}
