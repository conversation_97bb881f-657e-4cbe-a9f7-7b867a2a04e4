"use server";

import { TenantAwareDB } from './db';

/**
 * Calculate athlete balance based on transactions
 * Balance = Total Credits (balance top-ups) - Total Debits (balance usage) - Total Outstanding Payments
 * Positive balance = athlete has credit
 * Negative balance = athlete owes money
 * Zero balance = athlete is fully paid up
 */
export async function calculateAthleteBalance(athleteId: string, tenantId: string): Promise<number> {
  // Get all balance top-up transactions (paymentId is null - these add to balance)
  const totalCredits = await TenantAwareDB.getAthleteBalanceTopUpsSum(athleteId, tenantId);

  // Get all balance usage transactions (existing_balance method - these subtract from balance)
  const totalUsage = await TenantAwareDB.getAthleteBalanceUsageSum(athleteId, tenantId);

  // Get all outstanding payments (pending, overdue, partially_paid)
  const totalOutstanding = await TenantAwareDB.getAthleteOutstandingPaymentsSum(athleteId, tenantId);

  // Return balance: credits - usage - outstanding = available balance
  return totalCredits - totalUsage - totalOutstanding;
}

/**
 * Update an athlete's balance in the database
 */
export async function updateAthleteBalance(athleteId: string, tenantId: string, userId?: bigint): Promise<number> {
  const calculatedBalance = await calculateAthleteBalance(athleteId, tenantId);
  
  await TenantAwareDB.updateAthlete(
    athleteId,
    {
      balance: calculatedBalance.toFixed(2),
      updatedAt: new Date(),
      ...(userId && { updatedBy: userId }),
    },
    tenantId,
    userId
  );

  return calculatedBalance;
}

/**
 * Get detailed balance breakdown for an athlete
 */
export async function getAthleteBalanceBreakdown(athleteId: string, tenantId: string) {
  const paymentBreakdown = await TenantAwareDB.getAthletePaymentBreakdown(athleteId, tenantId);

  const breakdown = {
    completed: { count: 0, total: 0 },
    pending: { count: 0, total: 0 },
    overdue: { count: 0, total: 0 },
    cancelled: { count: 0, total: 0 },
    partially_paid: { count: 0, total: 0 },
  };

  paymentBreakdown.forEach((item: { status: string; count: number; total: number }) => {
    if (item.status in breakdown) {
      breakdown[item.status as keyof typeof breakdown] = {
        count: Number(item.count),
        total: parseFloat(item.total?.toString() || "0"),
      };
    }
  });

  // Calculate outstanding balance using the new transaction-based approach
  const calculatedBalance = await calculateAthleteBalance(athleteId, tenantId);

  return {
    breakdown,
    calculatedBalance,
    totalPayments: Object.values(breakdown).reduce((sum, item) => sum + item.count, 0),
    totalAmount: Object.values(breakdown).reduce((sum, item) => sum + item.total, 0),
  };
}
