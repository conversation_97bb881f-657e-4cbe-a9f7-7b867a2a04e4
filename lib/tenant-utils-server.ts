import { getServerSession } from 'next-auth';
import { headers } from 'next/headers';
import { unstable_noStore as noStore } from 'next/cache';
import { authOptions } from '@/lib/auth-config';
import { extractTenantIdFromSession, extractUserIdFromSession, extractTenantId, extractUserId } from './middleware-tenant-utils';
import logger from '@/lib/logger';

// ========================================
// SERVER-SIDE UTILITIES
// Note: This file contains server-only utilities that use next/headers
// These should only be imported in server components, API routes, or server actions
// ========================================

/**
 * Get tenant ID from server-side context (API routes, server components)
 * This function works in both API routes and server components
 * @returns Promise<string | null> - The tenant ID or null if not found
 */
/**
 * Get tenant ID from server-side context (API routes, server components)
 * This function works in both API routes and server components
 * @returns Promise<string | null> - The tenant ID or null if not found
 */
export async function getServerTenantId(): Promise<string | null> {
  try {
    // First try to get tenant ID from headers (set by middleware)
    // This will throw during static generation, which is expected
    const headersList = await headers();
    
    // Only call noStore() if we successfully got headers
    noStore();
    
    const tenantIdFromHeader = headersList.get('x-tenant-id');
    
    if (tenantIdFromHeader) {
      return tenantIdFromHeader;
    }
    
    // Fallback: get from session (preferred over parsing token)
    const session = await getServerSession(authOptions);
    if (session) {
      // Try to get from session first (already parsed)
      const tenantIdFromSession = extractTenantIdFromSession(session);
      if (tenantIdFromSession) {
        return tenantIdFromSession;
      }
      
      // Last resort: parse token
      if (session.accessToken) {
        return extractTenantId(session as any);
      }
    }
    
    return null;
  } catch (error) {
    // During build time, headers() will throw - return null to allow static generation
    // This is expected behavior during static generation
    return null;
  }
}

/**
 * Require tenant ID in server context, throw error if not found
 * @returns Promise<string> - The tenant ID
 * @throws Error if tenant ID is not found
 */
export async function requireServerTenantId(): Promise<string> {
  const tenantId = await getServerTenantId();
  if (!tenantId) {
    throw new Error('Tenant ID is required but not found in server context');
  }
  return tenantId;
}

/**
 * Get tenant ID from API request headers
 * @param request - The request object
 * @returns The tenant ID or null if not found
 */
export function getTenantIdFromApiRequest(request: Request): string | null {
  return request.headers.get('x-tenant-id');
}

/**
 * Require tenant ID from API request, throw error if not found
 * @param request - The request object
 * @returns The tenant ID
 * @throws Error if tenant ID is not found
 */
export function requireTenantIdFromApiRequest(request: Request): string {
  const tenantId = getTenantIdFromApiRequest(request);
  if (!tenantId) {
    throw new Error('Tenant ID is required but not found in request headers');
  }
  return tenantId;
}

/**
 * Get user ID from server-side context (API routes, server components)
 * @returns Promise<bigint | null> - The user ID or null if not found
 */
export async function getServerUserId(): Promise<bigint | null> {
  try {
    // First try to get user ID from headers (set by middleware)
    // This will throw during static generation, which is expected
    const headersList = await headers();
    const userIdFromHeader = headersList.get('x-user-id');
    
    if (userIdFromHeader) {
      try {
        return BigInt(userIdFromHeader);
      } catch (error) {
        logger.error('Error converting user ID from header to bigint:', { error });
      }
    }
    
    // Fallback: get from session (preferred over parsing token)
    const session = await getServerSession(authOptions);
    if (session) {
      // Try to get from session first (already parsed)
      const userIdFromSession = extractUserIdFromSession(session);
      if (userIdFromSession) {
        return userIdFromSession;
      }
      
      // Last resort: parse token
      if (session.accessToken) {
        return extractUserId(session as any);
      }
    }
    
    return null;
  } catch (error) {
    // During build time, headers() will throw - return null to allow static generation
    return null;
  }
}

/**
 * Require user ID in server context, throw error if not found
 * @returns Promise<bigint> - The user ID
 * @throws Error if user ID is not found
 */
export async function requireServerUserId(): Promise<bigint> {
  const userId = await getServerUserId();
  if (!userId) {
    throw new Error('User ID is required but not found in server context');
  }
  return userId;
}

/**
 * Get user ID from API request headers
 * @param request - The request object
 * @returns The user ID or null if not found
 */
export function getUserIdFromApiRequest(request: Request): bigint | null {
  const userIdHeader = request.headers.get('x-user-id');
  if (userIdHeader) {
    try {
      return BigInt(userIdHeader);
    } catch (error) {
      logger.error('Error converting user ID from API request to bigint:', { error });
    }
  }
  return null;
}

/**
 * Require user ID from API request, throw error if not found
 * @param request - The request object
 * @returns The user ID
 * @throws Error if user ID is not found
 */
export function requireUserIdFromApiRequest(request: Request): bigint {
  const userId = getUserIdFromApiRequest(request);
  if (!userId) {
    throw new Error('User ID is required but not found in request headers');
  }
  return userId;
}
