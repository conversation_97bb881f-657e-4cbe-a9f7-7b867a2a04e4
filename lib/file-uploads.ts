"use server";

import { writeFile, unlink, mkdir } from 'fs/promises';
import { join } from 'path';
import { getServerTenantId } from './tenant-utils-server';
import logger from '@/lib/logger';

// File upload actions
export async function uploadImage(formData: FormData): Promise<string> {
  try {
    const file = formData.get('file') as File;
    if (!file) {
      throw new Error('No file provided');
    }

    // Get tenant ID for tenant-specific uploads
    const tenantId = await getServerTenantId();
    if (!tenantId) {
      throw new Error('Tenant ID not found. User must be authenticated.');
    }

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      throw new Error('Invalid file type. Only JPEG, PNG, and WebP are allowed.');
    }

    // Validate file size (5MB max)
    if (file.size > 5000000) {
      throw new Error('File size too large. Maximum 5MB allowed.');
    }

    // Generate unique filename
    const bytes = await file.arrayBuffer();
    const buffer = new Uint8Array(bytes);
    const filename = `${Date.now()}-${file.name}`;
    
    // Create tenant-specific directory path in private directory (not publicly accessible)
    const tenantUploadDir = join(process.cwd(), 'private', 'uploads', tenantId);
    const filePath = join(tenantUploadDir, filename);

    // Ensure tenant-specific upload directory exists
    try {
      await mkdir(tenantUploadDir, { recursive: true });
    } catch (error) {
      logger.error('Error creating tenant upload directory:', { error });
      // Continue if directory already exists
    }

    await writeFile(filePath, buffer);

    // Get base URL from environment variable or default
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';

    // Return secure URL that goes through authentication
    return `${baseUrl}/api/secure-images/${tenantId}/${filename}`;
  } catch (error) {
    logger.error('Upload error:', { error });
    throw error;
  }
}

export async function deleteImage(imagePath: string): Promise<void> {
  try {
    if (!imagePath) return;

    // Get tenant ID to validate the image belongs to the current tenant
    const tenantId = await getServerTenantId();
    if (!tenantId) {
      logger.error('Tenant ID not found for image deletion');
      return;
    }

    let tenantIdFromPath = '';
    let filename = '';

    // Handle different URL formats
    if (imagePath.includes('/api/secure-images/')) {
      // New secure URL format: https://domain.com/api/secure-images/tenantId/filename
      try {
        const url = new URL(imagePath);
        const pathSegments = url.pathname.split('/');
        if (pathSegments.length >= 5 && pathSegments[2] === 'secure-images') {
          tenantIdFromPath = pathSegments[3];
          filename = pathSegments[4];
        }
      } catch (error) {
        logger.error('Error parsing secure image URL:', { error });
        return;
      }
    } else if (imagePath.startsWith('/uploads/')) {
      // Legacy relative path format: /uploads/tenantId/filename
      const pathParts = imagePath.split('/');
      if (pathParts.length >= 4) {
        tenantIdFromPath = pathParts[2];
        filename = pathParts[3];
      }
    } else {
      logger.error('Unrecognized image path format:', { imagePath });
      return;
    }

    // Validate tenant ownership
    if (tenantIdFromPath !== tenantId) {
      logger.error('Tenant mismatch in image deletion:', { tenantIdFromPath, tenantId });
      return;
    }

    // Validate filename
    if (!filename || filename.includes('..') || filename.includes('/') || filename.includes('\\')) {
      logger.error('Invalid filename for deletion:', { filename });
      return;
    }

    // Try to delete from new private directory first
    try {
      const privatePath = join(process.cwd(), 'private', 'uploads', tenantId, filename);
      await unlink(privatePath);
      logger.info('Successfully deleted image from private directory:', { privatePath });
    } catch (error) {
      // If not found in private directory, try legacy public directory
      try {
        const publicPath = join(process.cwd(), 'public', 'uploads', tenantId, filename);
        await unlink(publicPath);
        logger.info('Successfully deleted legacy image from public directory:', { publicPath });
      } catch (legacyError) {
        logger.warn('Image file not found in either private or public directory:', { filename });
      }
    }
  } catch (error) {
    logger.error('Delete image error:', { error });
    // Don't throw error for file deletion failures
  }
}