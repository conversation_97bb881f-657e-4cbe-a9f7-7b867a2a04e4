
"use server";

import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth-config';
import logger from '@/lib/logger';
import axios from 'axios';

export async function serverLogout() {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      logger.warn('Server logout called without an active session.');
      return { success: false, error: 'Not authenticated' };
    }

    logger.info(`User logout initiated for session: ${session.user?.email || 'unknown'}`);

    const zitadelIssuer = process.env.ZITADEL_ISSUER;
    if (!zitadelIssuer) {
      logger.error('ZITADEL_ISSUER environment variable is not set');
      return { success: false, error: 'Server configuration error' };
    }

    const zitadelEndSessionUrl = new URL(`${zitadelIssuer}/oidc/v1/end_session`);

    if (session.idToken) {
      zitadelEndSessionUrl.searchParams.append('id_token_hint', session.idToken);
    }

    const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
    const postLogoutRedirectUri = `${baseUrl}/auth/signin`;
    zitadelEndSessionUrl.searchParams.append('post_logout_redirect_uri', postLogoutRedirectUri);

    // Revoke token on Zitadel side
    if (session.accessToken) {
      try {
        await axios.post(`${zitadelIssuer}/oauth/v2/revoke`, new URLSearchParams({
          client_id: process.env.ZITADEL_CLIENT_ID!,
          token: session.accessToken as string,
        }), {
          headers: {
            "Content-Type": "application/x-www-form-urlencoded",
          },
        });
        logger.info('Access token successfully revoked on Zitadel.');
      } catch (revokeError) {
        logger.error('Error revoking token on Zitadel:', { revokeError });
        // Continue with logout even if token revocation fails
      }
    }

    logger.info(`User logout completed successfully for: ${session.user?.email || 'unknown'}`);
    return { success: true, logoutUrl: zitadelEndSessionUrl.toString() };

  } catch (error) {
    logger.error('Error during server-side logout:', { error });
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error during logout' };
  }
}
