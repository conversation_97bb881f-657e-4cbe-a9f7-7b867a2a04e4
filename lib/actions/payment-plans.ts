"use server";

import { revalidatePath } from 'next/cache';
import { paymentPlanService } from '../services';
import logger from '@/lib/logger';

export async function createPaymentPlan(values: {
  name: string;
  monthlyValue: string;
  assignDay: number;
  dueDay: number;
  status?: 'active' | 'inactive';
  description?: string;
  selectedBranches?: string[];
}) {
  try {
    const service = paymentPlanService();
    const result = await service.createPaymentPlan(values);

    if (!result.success) {
      logger.error("createPaymentPlan error:", { error: result.error });
      if(result.error?.details && result.error.details.rule){
        return { success: false, error: result.error?.details?.rule , errorType: result.error?.cause?.name };
      }
      return { success: false, error: result.error?.userMessage || 'Failed to create payment plan' , errorType: 'general' };
    }else{
      revalidatePath('/payment-plans');
      return { success: true, data: result.data };
    }
  } catch (error) {
    logger.error('Error creating payment plan:', { error });
    return { success: false, error: error instanceof Error ? error.message : "Failed to create payment plan", errorType: 'general' };
  }
}

export async function updatePaymentPlan(id: string, values: {
  name?: string;
  monthlyValue?: string;
  assignDay?: number;
  dueDay?: number;
  status?: 'active' | 'inactive';
  description?: string;
  selectedBranches?: string[];
}) {
  try {
    const service = paymentPlanService();
    const result = await service.updatePaymentPlan(id, values);

    if (!result.success) {
      logger.error("updatePaymentPlan error:", { error: result.error });
      if(result.error?.details && result.error.details.rule){
        return { success: false, error: result.error?.details?.rule , errorType: result.error?.cause?.name };
      }
      return { success: false, error: result.error?.userMessage || 'Failed to update payment plan' , errorType: 'general' };
    }else{
      revalidatePath('/payment-plans');
      revalidatePath(`/payment-plans/${id}`);
      return { success: true, data: result.data };
    }
  } catch (error) {
    logger.error('Error updating payment plan:', { error });
    return { success: false, error: error instanceof Error ? error.message : "Failed to update payment plan", errorType: 'general' };
  }
}

export async function getPaymentPlanAssignedAthletesCount(id: string): Promise<number> {
  const service = paymentPlanService();
  const result = await service.getPaymentPlanAssignedAthletesCount(id);

  if (!result.success) {
    logger.error('Error getting payment plan assigned athletes count:', { error: result.error });
    return 0;
  }

  return result.data || 0;
}

export async function deletePaymentPlan(id: string) {
  try {
    const service = paymentPlanService();
    const result = await service.deletePaymentPlan(id);

    if (!result.success) {
      logger.error(`Failed to delete payment plan with ID ${id} deletePaymentPlan error:`, { error: result.error });
      if(result.error?.details && result.error.details.rule){
        return { success: false, error: result.error?.details?.rule , errorType: result.error?.cause?.name };
      }
      return { success: false, error: result.error?.userMessage || 'Failed to delete payment plan' , errorType: 'general' };
    }else{
      revalidatePath('/payment-plans');
      return { success: true };
    }
  } catch (error) {
    logger.error('Error deleting payment plan:', { error });
    return { success: false, error: error instanceof Error ? error.message : "Failed to delete payment plan", errorType: 'general' };
  }
}

export async function getPaymentPlans() {
  try {
    const service = paymentPlanService();
    const result = await service.getPaymentPlans();

    if (!result.success) {
      logger.error("getPaymentPlans error:", { error: result.error });
      throw new Error(result.error?.userMessage || "Failed to get payment plans");
    }

    return result.data || [];
  } catch (error) {
    logger.error('Error getting payment plans:', { error });
    throw error;
  }
}

export async function getPaymentPlanById(id: string) {
  try {
    const service = paymentPlanService();
    const result = await service.getPaymentPlanById(id);

    if (!result.success) {
      logger.error("getPaymentPlanById error:", { error: result.error });
      throw new Error(result.error?.userMessage || `Failed to get payment plan with ID ${id}`);
    }

    return result.data;
  } catch (error) {
    logger.error('Error getting payment plan by ID:', { error });
    throw error;
  }
}

export async function getPaymentsByPlanId(planId: string) {
  const service = paymentPlanService();
  const result = await service.getPaymentsByPlanId(planId);

  if (!result.success) {
    throw new Error(result.error?.message || 'Failed to get payments by plan ID');
  }

  return result.data;
}

export async function getPaymentPlanStats(planId: string) {
  const service = paymentPlanService();
  const result = await service.getPaymentPlanStats(planId);

  if (!result.success) {
    throw new Error(result.error?.message || 'Failed to get payment plan stats');
  }

  return result.data;
}

export async function validatePaymentPlanData(data: {
  name: string;
  monthlyValue: string;
  assignDay: number;
  dueDay: number;
}): Promise<{ isValid: boolean; errors: string[] }> {
  try {
    const service = paymentPlanService();
    return await service.validatePaymentPlanData(data);
  } catch (error) {
    logger.error('Error validating payment plan data:', { error });
    return { isValid: false, errors: ['An error occurred while validating the data. Please try again later.'] };
  }
}
