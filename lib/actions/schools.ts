"use server";

import { schoolService } from '../services';
import { getServerTenantId } from '../tenant-utils-server';
import logger from '@/lib/logger';

// Schools
export async function getSchools() {
  try {
    const tenantId = await getServerTenantId();
    const result = await schoolService().getSchools(undefined, tenantId || undefined);
    
    if (!result.success) {
      logger.error("getSchools error:", { error: result.error });
      throw new Error(result.error?.userMessage || "Failed to get schools");
    }
    
    return result.data || [];
  } catch (error) {
    logger.error("getSchools error:", { error });
    throw error;
  }
}

export async function getSchoolById(id: string) {
  try {
    const tenantId = await getServerTenantId();
    const result = await schoolService().getSchoolById(id, undefined, tenantId || undefined);
    
    if (!result.success) {
      logger.error("getSchoolById error:", { error: result.error });
      throw new Error(result.error?.userMessage || `Failed to get school with ID ${id}`);
    }
    
    return result.data;
  } catch (error) {
    logger.error("getSchoolById error:", { error });
    throw error;
  }
}

export async function updateSchool(id: string, data: { name?: string; address?: string; phone?: string; email?: string; logo?: string; foundedYear?: number; branches?: string[] }) {
  try {
    logger.info("updateSchool called with:", { id, data });
    const tenantId = await getServerTenantId();
    
    // Use the service method that handles branches
    const result = await schoolService().updateSchoolWithBranches(
      id, 
      data, 
      undefined, 
      tenantId || undefined
    );
    
    if (!result.success) {
      logger.error("updateSchool error:", { error: result.error });
      throw new Error(result.error?.userMessage || `Failed to update school with ID ${id}`);
    }
    
    logger.info("updateSchool result:", { data: result.data });
    return result.data;
  } catch (error) {
    logger.error("updateSchool error:", { error });
    throw error;
  }
}

export async function createSchool(data: { name: string; foundedYear: number; address?: string; phone?: string; email?: string; logo?: string; branches?: string[] }) {
  try {
    logger.info("createSchool called with:", { data });
    const tenantId = await getServerTenantId();
    
    // Use the service method that handles branches
    const result = await schoolService().createSchoolWithBranches(
      data, 
      undefined, 
      tenantId || undefined
    );
    
    if (!result.success) {
      logger.error("createSchool error:", { error: result.error });
      throw new Error(result.error?.userMessage || "Failed to create school");
    }
    
    logger.info("createSchool completed successfully");
    
    // Return a serializable success response
    return { success: true };
  } catch (error) {
    logger.error("createSchool error:", { error });
    throw error;
  }
}

export async function deleteSchool(id: string) {
  try {
    logger.info("deleteSchool called with id:", { id });
    const tenantId = await getServerTenantId();
    
    const result = await schoolService().deleteSchool(id, undefined, tenantId || undefined);
    
    if (!result.success) {
      logger.error(`Failed to delete school with ID ${id}. deleteSchool error:`, { error: result.error });
      if(result.error?.details && result.error.details.rule){
        return { success: false, error: result.error?.details?.rule , errorType: result.error?.cause?.name };
      }
      return { success: false, error: result.error?.userMessage || 'Failed to delete school' , errorType: 'general' };
    }else{
      logger.info("deleteSchool result:", { data: result.data });
      // Return a serializable success response
      return { success: true };
    }
  } catch (error : any) {
    logger.error("deleteSchool error:", { error });
    return { success: false, error: error.message };
  }
}