"use server";

import { revalidatePath } from "next/cache";
import { getServerTenantId, getServerUserId } from "../tenant-utils-server";
import { balanceProcessorService } from "@/lib/services/balance-processor.service";
import logger from "@/lib/logger";

export async function processNewPaymentBalanceAllocation(paymentId: string) {
  try {
    const tenantId = await getServerTenantId();
    const userId = await getServerUserId();
    
    const result = await balanceProcessorService().processNewPaymentBalanceAllocation(
      paymentId,
      undefined,
      userId?.toString(),
      tenantId || undefined
    );
    
    if (!result.success) {
      logger.error("processNewPaymentBalanceAllocation error:", { error: result.error });
      throw new Error(result.error?.userMessage || "Failed to process balance allocation");
    }
    
    revalidatePath("/payments");
    return result.data;
  } catch (error) {
    logger.error("processNewPaymentBalanceAllocation error:", error);
    throw error;
  }
}

export async function processAthleteBalanceAllocation(athleteId: string) {
  try {
    const tenantId = await getServerTenantId();
    const userId = await getServerUserId();
    
    const result = await balanceProcessorService().processAthleteBalanceAllocation(
      athleteId,
      undefined,
      userId?.toString(),
      tenantId || undefined
    );
    
    if (!result.success) {
      logger.error("processAthleteBalanceAllocation error:", { error: result.error });
      throw new Error(result.error?.userMessage || "Failed to process athlete balance allocation");
    }
    
    revalidatePath("/payments");
    revalidatePath("/athletes");
    return result.data;
  } catch (error) {
    logger.error("processAthleteBalanceAllocation error:", error);
    throw error;
  }
}

export async function getAthleteBalanceSummary(athleteId: string) {
  try {
    const tenantId = await getServerTenantId();
    
    const result = await balanceProcessorService().getAthleteBalanceSummary(
      athleteId,
      undefined,
      tenantId || undefined
    );
    
    if (!result.success) {
      logger.error("getAthleteBalanceSummary error:", { error: result.error });
      throw new Error(result.error?.userMessage || "Failed to get athlete balance summary");
    }
    
    return result.data;
  } catch (error) {
    logger.error("getAthleteBalanceSummary error:", error);
    throw error;
  }
}
