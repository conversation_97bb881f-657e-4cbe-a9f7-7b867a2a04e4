"use server";

import { financialService } from '../services';
import { getServerTenantId } from '../tenant-utils-server';
import logger from '@/lib/logger';

// Financial Summary
export async function getFinancialSummary() {
  try {
    const tenantId = await getServerTenantId();
    const result = await financialService().getFinancialSummary(tenantId || undefined);
    
    if (!result.success) {
      logger.error("getFinancialSummary error:", { error: result.error });
      throw new Error(result.error?.userMessage || "Failed to get financial summary");
    }
    
    return result.data;
  } catch (error) {
    logger.error("getFinancialSummary error:", { error });
    throw error;
  }
}