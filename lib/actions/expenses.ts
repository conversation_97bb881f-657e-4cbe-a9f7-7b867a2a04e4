"use server";

import { expenseService } from '../services/expense.service';
import { revalidatePath } from 'next/cache';
import logger from '@/lib/logger';

// Expenses
export async function getExpenses() {
  try {
    // Try using the service first
    const service = expenseService();
    const result = await service.getExpenses();

    if (result.success) {
      return result.data;
    }

    // If service fails, log error and throw
    logger.error("Service call failed:", { error: result.error });
    throw new Error(result.error?.userMessage || "Failed to get expenses");
  } catch (error) {
    logger.error("Error getting expenses:", { error });
    throw error;
  }
}

export async function getExpensesPaginated(options: {
  page?: string;
  limit?: string;
  search?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  category?: string;
  fromDate?: string;
  toDate?: string;
  instructorId?: string;
  facilityId?: string;
}) {
  // Parse pagination parameters
  const parsedOptions = {
    page: options.page ? parseInt(options.page) : 1,
    limit: options.limit ? parseInt(options.limit) : 10,
    search: options.search || '',
    sortBy: options.sortBy || 'date',
    sortOrder: options.sortOrder || 'desc',
    category: options.category || '',
    fromDate: options.fromDate || '',
    toDate: options.toDate || '',
    instructorId: options.instructorId || '',
    facilityId: options.facilityId || '',
  };

  logger.info('Server fetching paginated expenses with params:', { parsedOptions });
  
  // Use the service layer
  const service = expenseService();
  const result = await service.getExpensesPaginated(parsedOptions);

  if (!result.success) {
    throw new Error(result.error?.message || 'Failed to get paginated expenses');
  }

  return result.data;
}

export async function getExpenseById(id: string) {
  try {
    // Try using the service first
    const service = expenseService();
    const result = await service.getExpenseById(id);

    if (result.success) {
      return result.data;
    }

    // If service fails, log error and throw
    logger.error("Service call failed:", { error: result.error });
    throw new Error(result.error?.userMessage || `Failed to get expense with ID ${id}`);
  } catch (error) {
    logger.error("Error getting expense by ID:", { error });
    throw error;
  }
}

export async function createExpense(data: {
  amount: string;
  date: string;
  category: 'salary' | 'insurance' | 'rent' | 'equipment' | 'other';
  description: string;
  instructorId?: string;
  facilityId?: string;
}) {
  try {
    // Try using the service first
    const service = expenseService();
    const result = await service.createExpense(data);

    if (result.success) {
      // Revalidate paths
      revalidatePath('/expenses');
      return { success: true, data: result.data };
    }else{
      logger.error("Service call failed:", { error: result.error });
      if(result.error?.details && result.error.details.rule){
        return { success: false, error: result.error?.details?.rule , errorType: result.error?.cause?.name };
      }
      return { success: false, error: result.error?.userMessage || 'Failed to create expense' , errorType: 'general' };
    }
  } catch (error) {
    logger.error("Error creating expense:", { error });
    return { success: false, error: error instanceof Error ? error.message : "Failed to create expense", errorType: 'general' };
  }
}

export async function updateExpense(id: string, data: {
  amount?: string;
  date?: string;
  category?: 'salary' | 'insurance' | 'rent' | 'equipment' | 'other';
  description?: string;
  instructorId?: string;
  facilityId?: string;
}) {
  try {
    // Try using the service first
    const service = expenseService();
    const result = await service.updateExpense(id, data);

    if (result.success) {
      // Revalidate paths
      revalidatePath('/expenses');
      revalidatePath(`/expenses/${id}`);
      return { success: true, data: result.data };
    }else{
      logger.error("Service call failed:", { error: result.error });
      if(result.error?.details && result.error.details.rule){
        return { success: false, error: result.error?.details?.rule , errorType: result.error?.cause?.name };
      }
      return { success: false, error: result.error?.userMessage || 'Failed to update expense' , errorType: 'general' };
    }
  } catch (error) {
    logger.error("Error updating expense:", { error });
    return { success: false, error: error instanceof Error ? error.message : "Failed to update expense", errorType: 'general' };
  }
}

export async function deleteExpense(id: string) {
  try {
    // Try using the service first
    const service = expenseService();
    const result = await service.deleteExpense(id);

    if (result.success) {
      // Revalidate paths
      revalidatePath('/expenses');
      return true;
    }

    // If service fails, log error and throw
    logger.error("Service call failed:", { error: result.error });
    throw new Error(result.error?.userMessage || `Failed to delete expense with ID ${id}`);
  } catch (error) {
    logger.error("Error deleting expense:", { error });
    throw error;
  }
}