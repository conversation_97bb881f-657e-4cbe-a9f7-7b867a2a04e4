"use server";

import { getServerTenantId } from '../tenant-utils-server';
import { branchService } from '../services';
import logger from '@/lib/logger';

export async function getBranches() {
  try {
    const tenantId = await getServerTenantId();
    const result = await branchService().getBranches(undefined, tenantId || undefined);
    
    if (!result.success) {
      logger.error("getBranches error:", { error: result.error });
      throw new Error(result.error?.userMessage || "Failed to get branches");
    }
    
    return result.data || [];
  } catch (error) {
    logger.error("getBranches error:", { error });
    throw error;
  }
}
