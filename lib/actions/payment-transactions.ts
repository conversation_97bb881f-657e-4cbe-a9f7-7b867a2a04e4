"use server";

import { revalidatePath } from "next/cache";
import { getServerTenantId, getServerUserId } from "../tenant-utils-server";
import { paymentTransactionService } from "@/lib/services/payment-transaction.service";
import logger from "@/lib/logger";

export async function getPaymentTransactions() {
  try {
    const tenantId = await getServerTenantId();
    
    const result = await paymentTransactionService().getPaymentTransactions(
      undefined,
      tenantId || undefined
    );
    
    if (!result.success) {
      logger.error("getPaymentTransactions error:", { error: result.error });
      throw new Error(result.error?.userMessage || "Failed to fetch payment transactions");
    }
    
    return result.data;
  } catch (error) {
    logger.error("getPaymentTransactions error:", error);
    throw error;
  }
}

export async function getPaymentTransactionsPaginated(
  page: number = 1,
  limit: number = 10,
  search?: string,
  sortBy: string = 'transactionDate',
  sortOrder: 'asc' | 'desc' = 'desc',
  filters: Record<string, string> = {}
) {
  try {
    const tenantId = await getServerTenantId();
    
    const result = await paymentTransactionService().getPaymentTransactionsPaginated(
      page,
      limit,
      search,
      sortBy,
      sortOrder,
      filters,
      undefined,
      tenantId || undefined
    );
    
    if (!result.success) {
      logger.error("getPaymentTransactionsPaginated error:", { error: result.error });
      throw new Error(result.error?.userMessage || "Failed to fetch payment transactions");
    }
    
    return result.data;
  } catch (error) {
    logger.error("getPaymentTransactionsPaginated error:", error);
    throw error;
  }
}

export async function getPaymentTransactionById(id: string) {
  try {
    const tenantId = await getServerTenantId();
    
    const result = await paymentTransactionService().getPaymentTransactionById(
      id,
      undefined,
      tenantId || undefined
    );
    
    if (!result.success) {
      logger.error("getPaymentTransactionById error:", { error: result.error });
      throw new Error(result.error?.userMessage || "Failed to fetch payment transaction");
    }
    
    return result.data;
  } catch (error) {
    logger.error("getPaymentTransactionById error:", error);
    throw error;
  }
}

export async function getTransactionsByPaymentId(paymentId: string) {
  try {
    const tenantId = await getServerTenantId();
    
    const result = await paymentTransactionService().getTransactionsByPaymentId(
      paymentId,
      undefined,
      tenantId || undefined
    );
    
    if (!result.success) {
      logger.error("getTransactionsByPaymentId error:", { error: result.error });
      throw new Error(result.error?.userMessage || "Failed to fetch payment transactions");
    }
    
    return result.data;
  } catch (error) {
    logger.error("getTransactionsByPaymentId error:", error);
    throw error;
  }
}

export async function getTransactionsByAthleteId(athleteId: string) {
  try {
    const tenantId = await getServerTenantId();
    
    const result = await paymentTransactionService().getTransactionsByAthleteId(
      athleteId,
      undefined,
      tenantId || undefined
    );
    
    if (!result.success) {
      logger.error("getTransactionsByAthleteId error:", { error: result.error });
      throw new Error(result.error?.userMessage || "Failed to fetch athlete transactions");
    }
    
    return result.data;
  } catch (error) {
    logger.error("getTransactionsByAthleteId error:", error);
    throw error;
  }
}

export async function createPaymentTransaction(data: {
  athleteId: string;
  paymentId?: string | null;
  amount: string;
  transactionMethod: 'cash' | 'bank_transfer' | 'credit_card' | 'existing_balance';
  transactionDate?: Date;
  notes?: string | null;
}) {
  try {
    const tenantId = await getServerTenantId();
    const userId = await getServerUserId();
    
    const result = await paymentTransactionService().createPaymentTransaction(
      data,
      tenantId || undefined,
      userId?.toString()
    );
    
    if (!result.success) {
      logger.error("createPaymentTransaction error:", { error: result.error });
      throw new Error(result.error?.userMessage || "Failed to create payment transaction");
    }
    
    revalidatePath("/payments");
    return result.data;
  } catch (error) {
    logger.error("createPaymentTransaction error:", error);
    throw error;
  }
}

export async function updatePaymentTransaction(id: string, data: {
  athleteId?: string;
  paymentId?: string | null;
  amount?: string;
  transactionMethod?: 'cash' | 'bank_transfer' | 'credit_card' | 'existing_balance';
  transactionDate?: Date;
  notes?: string | null;
}) {
  try {
    const tenantId = await getServerTenantId();
    const userId = await getServerUserId();
    
    const result = await paymentTransactionService().updatePaymentTransaction(
      id,
      data,
      undefined,
      userId?.toString(),
      tenantId || undefined
    );
    
    if (!result.success) {
      logger.error("updatePaymentTransaction error:", { error: result.error });
      throw new Error(result.error?.userMessage || "Failed to update payment transaction");
    }
    
    revalidatePath("/payments");
    return result.data;
  } catch (error) {
    logger.error("updatePaymentTransaction error:", error);
    throw error;
  }
}

export async function deletePaymentTransaction(id: string) {
  try {
    const tenantId = await getServerTenantId();
    
    const result = await paymentTransactionService().deletePaymentTransaction(
      id,
      undefined,
      tenantId || undefined
    );
    
    if (!result.success) {
      logger.error("deletePaymentTransaction error:", { error: result.error });
      throw new Error(result.error?.userMessage || "Failed to delete payment transaction");
    }
    
    revalidatePath("/payments");
    return result.data;
  } catch (error) {
    logger.error("deletePaymentTransaction error:", error);
    throw error;
  }
}

export async function processPaymentWithTransaction(
  paymentId: string,
  transactionData: {
    amount: string;
    transactionMethod: 'cash' | 'bank_transfer' | 'credit_card';
    notes?: string | null;
  }
) {
  try {
    const tenantId = await getServerTenantId();
    const userId = await getServerUserId();
    
    const result = await paymentTransactionService().processPaymentWithTransaction(
      paymentId,
      transactionData,
      undefined,
      userId?.toString(),
      tenantId || undefined
    );
    
    if (!result.success) {
      logger.error("processPaymentWithTransaction error:", { error: result.error });
      throw new Error(result.error?.userMessage || "Failed to process payment");
    }
    
    revalidatePath("/payments");
    return result.data;
  } catch (error) {
    logger.error("processPaymentWithTransaction error:", error);
    throw error;
  }
}

export async function addAthleteBalance(
  athleteId: string,
  amount: string,
  transactionMethod: 'cash' | 'bank_transfer' | 'credit_card',
  notes?: string | null
) {
  try {
    const tenantId = await getServerTenantId();
    const userId = await getServerUserId();
    
    const result = await paymentTransactionService().addAthleteBalance(
      athleteId,
      amount,
      transactionMethod,
      notes,
      undefined,
      userId?.toString(),
      tenantId || undefined
    );
    
    if (!result.success) {
      logger.error("addAthleteBalance error:", { error: result.error });
      throw new Error(result.error?.userMessage || "Failed to add athlete balance");
    }
    
    revalidatePath("/payments");
    revalidatePath("/athletes");
    return result.data;
  } catch (error) {
    logger.error("addAthleteBalance error:", error);
    throw error;
  }
}
