import { getToken } from 'next-auth/jwt';
import { getSession } from 'next-auth/react';
import { NextRequest } from 'next/server';
import { JWT } from 'next-auth/jwt';
// Import session extraction functions from middleware utilities to avoid duplication
import { extractTenantIdFromSession, extractUserIdFromSession, extractTenantId, extractUserId } from './middleware-tenant-utils';
import logger from '@/lib/logger';

// ========================================
// SESSION-BASED EXTRACTION (OPTIMIZED)
// Note: Session extraction functions are imported from middleware-tenant-utils.ts
// to avoid code duplication while maintaining Edge Runtime compatibility
// ========================================

// Session extraction functions are now imported from middleware-tenant-utils.ts

// ========================================
// TOKEN-BASED EXTRACTION (FALLBACK)
// Note: extractTenantId and extractUserId are imported from middleware-tenant-utils.ts
// ========================================

/**
 * Extract all tenant IDs that a user has access to from Zitadel JWT token
 * @param token - The JWT token from NextAuth
 * @returns Array of tenant IDs or empty array if none found
 */
export function extractAllTenantIds(token: JWT | null): string[] {
  if (!token) return [];
  
  const tenantIds = new Set<string>();
  
  // Check in the ID token for roles structure
  if (token.idToken && typeof token.idToken === 'string') {
    try {
      const payload = JSON.parse(atob(token.idToken.split('.')[1]));
      
      // Extract from roles structure:
      // payload["urn:zitadel:iam:org:project:roles"] = {
      //   "DEFAULT_ROLE": {
      //     "321151237678497795": "zitadel.auth.sitedenemlak.com"
      //   }
      // }
      const roles = payload['urn:zitadel:iam:org:project:roles'];
      if (roles && typeof roles === 'object') {
        for (const roleName in roles) {
          const roleData = roles[roleName];
          if (roleData && typeof roleData === 'object') {
            for (const tenantId in roleData) {
              tenantIds.add(tenantId);
            }
          }
        }
      }
    } catch (error) {
      logger.error('Error decoding ID token for all tenant IDs:', { error });
    }
  }
  
  // Check for roles in the main token structure
  const roles = (token as any)['urn:zitadel:iam:org:project:roles'];
  if (roles && typeof roles === 'object') {
    for (const roleName in roles) {
      const roleData = roles[roleName];
      if (roleData && typeof roleData === 'object') {
        for (const tenantId in roleData) {
          tenantIds.add(tenantId);
        }
      }
    }
  }
  
  return Array.from(tenantIds);
}

// ========================================
// CLIENT-SIDE REQUEST UTILITIES
// ========================================

/**
 * Get tenant ID from request
 * @param request - NextRequest object
 * @returns The tenant ID or null if not found
 */
export async function getTenantIdFromRequest(request: NextRequest): Promise<string | null> {
  try {
    const token = await getToken({ 
      req: request,
      secret: process.env.NEXTAUTH_SECRET,
      secureCookie: process.env.NODE_ENV === 'production'
    });
    
    return extractTenantId(token);
  } catch (error) {
    logger.error('Error extracting tenant ID from request:', { error });
    return null;
  }
}

// ========================================
// VALIDATION & UTILITY FUNCTIONS
// ========================================

/**
 * Validate that a user has access to a specific tenant
 * @param userTenantId - The user's tenant ID
 * @param resourceTenantId - The tenant ID of the resource being accessed
 * @returns true if user has access, false otherwise
 */
export function validateTenantAccess(userTenantId: string | null, resourceTenantId: string): boolean {
  if (!userTenantId) return false;
  return userTenantId === resourceTenantId;
}

/**
 * Ensure that a tenant ID is provided, throw error if not
 * @param tenantId - The tenant ID to validate
 * @throws Error if tenant ID is null or empty
 */
export function requireTenantId(tenantId: string | null): asserts tenantId is string {
  if (!tenantId) {
    throw new Error('Tenant ID is required but not found in token');
  }
}

/**
 * Create a database filter condition for tenant isolation
 * @param tenantId - The tenant ID to filter by
 * @returns Object that can be used in Drizzle where conditions
 */
export function createTenantFilter(tenantId: string) {
  return { tenantId };
}

// ========================================
// USER ID EXTRACTION & UTILITIES
// Note: extractUserId is imported from middleware-tenant-utils.ts
// ========================================

/**
 * Get user ID from request
 * @param request - NextRequest object
 * @returns The user ID as bigint or null if not found
 */
export async function getUserIdFromRequest(request: NextRequest): Promise<bigint | null> {
  try {
    const token = await getToken({ 
      req: request,
      secret: process.env.NEXTAUTH_SECRET,
      secureCookie: process.env.NODE_ENV === 'production'
    });
    
    return extractUserId(token);
  } catch (error) {
    logger.error('Error extracting user ID from request:', { error });
    return null;
  }
}

/**
 * Ensure that a user ID is provided, throw error if not
 * @param userId - The user ID to validate
 * @throws Error if user ID is null
 */
export function requireUserId(userId: bigint | null): asserts userId is bigint {
  if (!userId) {
    throw new Error('User ID is required but not found in token');
  }
}

// ========================================
// DEBUG & DEVELOPMENT UTILITIES
// ========================================

/**
 * Debug function to log the token structure for development purposes
 * @param token - The JWT token from NextAuth
 */
export function debugTokenStructure(token: JWT | null): void {
  if (!token) {
    logger.info('❌ No token provided');
    return;
  }
  
  logger.info('🔍 Token structure:');
  logger.info('- Token keys:', { keys: Object.keys(token) });
  
  if (token.idToken && typeof token.idToken === 'string') {
    try {
      const payload = JSON.parse(atob(token.idToken.split('.')[1]));
      logger.info('- ID Token payload keys:', { keys: Object.keys(payload) });
      
      const roles = payload['urn:zitadel:iam:org:project:roles'];
      if (roles) {
        logger.info('- Roles structure:', { roles: JSON.stringify(roles, null, 2) });
        
        // Extract tenant IDs for verification
        const tenantIds = new Set<string>();
        for (const roleName in roles) {
          const roleData = roles[roleName];
          if (roleData && typeof roleData === 'object') {
            for (const tenantId in roleData) {
              tenantIds.add(tenantId);
              logger.info(`- Found tenant ID: ${tenantId} in role: ${roleName}`);
            }
          }
        }
        logger.info(`- Total unique tenant IDs found: ${tenantIds.size}`);
      } else {
        logger.info('❌ No roles found in ID token');
      }
    } catch (error) {
      logger.error('❌ Error parsing ID token:', { error });
    }
  } else {
    logger.info('❌ No ID token found');
  }
  
  // Check if tenant ID is successfully extracted
  const extractedTenantId = extractTenantId(token);
  logger.info(`✅ Extracted tenant ID: ${extractedTenantId}`);
  
  const extractedUserId = extractUserId(token);
  logger.info(`✅ Extracted user ID: ${extractedUserId}`);
}

// ========================================
// END OF FILE
// Note: Server-side utilities have been moved to tenant-utils-server.ts
// This helps avoid client-side imports of next/headers
// ========================================