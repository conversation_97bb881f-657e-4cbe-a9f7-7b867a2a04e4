import { BaseService } from './base';
import { ServiceResult, ValidationError } from '../errors/types';
import { BusinessRuleError } from '../errors/errors';
import { getServerTenantId, getServerUserId } from '../tenant-utils-server';
import { SmsProvider, SendSmsParams, SmsMessage, SmsServiceConfig } from './sms/types';
// Import the default provider - this can be changed to use different providers
import { NetGsmProvider, MockSmsProvider } from '@/lib/services/sms';
import { SmsManagementService, SmsTemplateVariables } from './sms-management.service';
import { SmsLoggingService } from './sms-logging.service';
import { SmsBalanceService } from './sms-balance.service';
import { TenantAwareDB } from '../db';

export interface SendSmsServiceParams {
  /** Sender identifier (optional if default is configured) */
  senderIdentifier?: string;
  /** Encoding parameter (optional if default is configured) */
  encoding?: string;
  /** Array of messages to send */
  messages: SmsMessage[];
  /** Optional metadata for tracking */
  metadata?: Record<string, any>;
}

export interface SendPaymentReminderSmsParams {
  paymentIds: string[];
  templateType: 'pending' | 'overdue';
  customTemplate?: string;
  combinePayments?: boolean;
}

export interface SendTeamSmsParams {
  teamId: string;
  message: string;
  athleteIds?: string[]; // If provided, send only to these athletes
}

export interface SendCustomSmsParams {
  athleteIds: string[];
  message: string;
}

export interface SendTemplateSmsParams {
  templateType: 'general' | 'event' | 'meeting' | 'holiday' | 'custom';
  customTemplate?: string;
  athleteIds: string[];
}

export interface SmsServiceResult {
  /** Whether all messages were sent successfully */
  success: boolean;
  /** Number of messages sent successfully */
  sentCount: number;
  /** Number of messages that failed */
  failedCount: number;
  /** Total number of messages */
  totalCount: number;
  /** Array of message IDs for tracking */
  messageIds?: string[];
  /** Provider response data */
  providerData?: any;
}

/**
 * SMS Service
 * Handles SMS sending using the adapter pattern
 */
export class SmsService extends BaseService {
  private provider: SmsProvider;
  private config: SmsServiceConfig;
  private smsManagementService: SmsManagementService;
  private smsLoggingService: SmsLoggingService;
  private smsBalanceService: SmsBalanceService;

  constructor(config?: Partial<SmsServiceConfig>) {
    super('SmsService');

    // Initialize with Mock provider by default for testing
    // To use NetGSM provider, change this to new NetGsmProvider()
    this.provider = config?.provider || new MockSmsProvider();

    this.config = {
      provider: this.provider,
      defaultSender: config?.defaultSender || process.env.SMS_DEFAULT_SENDER || 'SPORTSCLUB',
      defaultEncoding: config?.defaultEncoding || process.env.SMS_DEFAULT_ENCODING || 'UTF-8',
      enabled: config?.enabled !== undefined ? config.enabled : process.env.SMS_ENABLED !== 'false',
      ...config
    };

    // Initialize related services
    this.smsManagementService = new SmsManagementService();
    this.smsLoggingService = new SmsLoggingService();
    this.smsBalanceService = new SmsBalanceService();
  }

  /**
   * Calculate SMS count based on message length
   * Standard SMS: 160 characters = 1 SMS
   * Longer messages are split into multiple SMS parts
   */
  private calculateSmsCount(messageLength: number): number {
    if (messageLength === 0) return 0;
    if (messageLength <= 160) return 1;
    if (messageLength <= 306) return 2;
    if (messageLength <= 459) return 3;
    if (messageLength <= 612) return 4;
    return Math.ceil(messageLength / 153); // For messages longer than 612 chars
  }

  /**
   * Calculate actual SMS credits needed for a processed message
   * This accounts for variable replacements and actual message length
   */
  private calculateActualCredits(processedMessage: string): number {
    return this.calculateSmsCount(processedMessage.length);
  }

  /**
   * Check SMS balance and throw error if insufficient
   */
  private async checkAndValidateSmsBalance(
    requiredCredits: number,
    userId?: string,
    tenantId?: string,
    context?: string
  ): Promise<void> {
    const balanceCheck = await this.smsBalanceService.checkSufficientBalance(
      requiredCredits,
      userId,
      tenantId
    );

    if (!balanceCheck.success || !balanceCheck.data) {
      // Get current balance for error message
      const currentBalanceResult = await this.smsBalanceService.getSmsBalance(userId, tenantId);
      const currentBalance = currentBalanceResult.success ? (currentBalanceResult.data?.balance || 0) : 0;

      const contextMessage = context ? ` ${context}` : '';
      throw new BusinessRuleError(
        'insufficient_balance',
        `Insufficient SMS balance.${contextMessage} Required: ${requiredCredits} credits, Available: ${currentBalance} credits`
      );
    }
  }

  /**
   * Send SMS messages
   */
  async sendSms(
    params: SendSmsServiceParams,
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<SmsServiceResult>> {
    // Create validation functions
    const validationFunctions = this.createSmsValidationFunctions();

    return this.executeWithValidation(
      'sendSms',
      params,
      validationFunctions,
      async (validatedParams) => {
        const effectiveUserId = userId || await getServerUserId();
        const effectiveTenantId = tenantId || await getServerTenantId();

        // Check if SMS service is enabled
        if (!this.config.enabled) {
          throw new BusinessRuleError(
            'sms_disabled',
            'SMS service is currently disabled'
          );
        }

        // Prepare SMS parameters with defaults
        const smsParams: SendSmsParams = {
          senderIdentifier: validatedParams.senderIdentifier || this.config.defaultSender || '',
          encoding: validatedParams.encoding || this.config.defaultEncoding || 'UTF-8',
          messages: validatedParams.messages
        };

        // Final validation
        if (!smsParams.senderIdentifier) {
          throw new BusinessRuleError(
            'missing_sender',
            'Sender identifier is required and no default is configured'
          );
        }

        // Send SMS using the provider
        const providerResponse = await this.provider.sendSms(smsParams);

        if (!providerResponse.success) {
          throw new BusinessRuleError(
            'sms_send_failed',
            `SMS sending failed: ${providerResponse.error}`,
            {
              userId: effectiveUserId ? String(effectiveUserId) : undefined,
              tenantId: effectiveTenantId || undefined,
              operation: 'sendSms',
              resource: 'sms',
              metadata: {
                providerError: providerResponse.error,
                providerErrorCode: providerResponse.errorCode,
                provider: this.provider.getProviderName()
              }
            }
          );
        }

        // Prepare service result
        const result: SmsServiceResult = {
          success: true,
          sentCount: validatedParams.messages.length,
          failedCount: 0,
          totalCount: validatedParams.messages.length,
          messageIds: providerResponse.messageIds,
          providerData: providerResponse.data
        };

        return result;
      },
      {
        userId,
        tenantId,
        resource: 'sms',
        metadata: {
          messageCount: params.messages?.length || 0,
          provider: this.provider.getProviderName(),
          ...params.metadata
        }
      }
    );
  }

  /**
   * Get SMS service status and configuration
   */
  async getServiceStatus(
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<{
    enabled: boolean;
    provider: string;
    configured: boolean;
    defaultSender?: string;
    defaultEncoding?: string;
  }>> {
    return this.executeOperation(
      'getServiceStatus',
      async () => {
        const isConfigured = this.provider instanceof NetGsmProvider 
          ? (this.provider as NetGsmProvider).isConfigured()
          : true; // Assume other providers are configured

        return {
          enabled: this.config.enabled || false,
          provider: this.provider.getProviderName(),
          configured: isConfigured,
          defaultSender: this.config.defaultSender,
          defaultEncoding: this.config.defaultEncoding
        };
      },
      {
        userId,
        tenantId,
        resource: 'sms_status'
      }
    );
  }



  /**
   * Create validation functions for executeWithValidation
   */
  private createSmsValidationFunctions(): Array<(data: SendSmsServiceParams) => ValidationError | null> {
    return [
      // Messages array validation
      (data: SendSmsServiceParams) => {
        if (!data.messages || !Array.isArray(data.messages)) {
          return {
            field: 'messages',
            message: 'Messages must be an array',
            value: data.messages
          };
        }
        return null;
      },
      // Messages not empty validation
      (data: SendSmsServiceParams) => {
        if (data.messages && data.messages.length === 0) {
          return {
            field: 'messages',
            message: 'At least one message is required',
            value: data.messages
          };
        }
        return null;
      },
      // Individual message validation
      (data: SendSmsServiceParams) => {
        if (data.messages && Array.isArray(data.messages)) {
          for (let i = 0; i < data.messages.length; i++) {
            const message = data.messages[i];
            if (!message.receiver) {
              return {
                field: `messages[${i}].receiver`,
                message: 'Receiver phone number is required',
                value: message.receiver
              };
            }
            if (!message.message) {
              return {
                field: `messages[${i}].message`,
                message: 'Message content is required',
                value: message.message
              };
            }
            // Basic phone number validation
            if (message.receiver && !/^\+?[\d\s\-\(\)]+$/.test(message.receiver)) {
              return {
                field: `messages[${i}].receiver`,
                message: 'Invalid phone number format',
                value: message.receiver
              };
            }
          }
        }
        return null;
      }
    ];
  }

  /**
   * Update SMS service configuration
   */
  updateConfig(newConfig: Partial<SmsServiceConfig>): void {
    this.config = {
      ...this.config,
      ...newConfig
    };

    if (newConfig.provider) {
      this.provider = newConfig.provider;
    }
  }

  /**
   * Get current provider name
   */
  getProviderName(): string {
    return this.provider.getProviderName();
  }

  /**
   * Get payments for SMS reminders (pending, overdue, partially paid)
   */
  async getPaymentsForSms(
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any[]>> {
    return this.executeOperation(
      'getPaymentsForSms',
      async () => {
        const effectiveTenantId = tenantId || await getServerTenantId();

        // Get pending, overdue, and partially paid payments with athlete details
        const [pendingPayments, overduePayments, partiallyPaidPayments] = await Promise.all([
          TenantAwareDB.getPendingPaymentsWithAthleteDetails(effectiveTenantId || undefined),
          TenantAwareDB.getOverduePaymentsWithAthleteDetails(effectiveTenantId || undefined),
          TenantAwareDB.getPartiallyPaidPaymentsWithAthleteDetails(effectiveTenantId || undefined)
        ]);

        // Combine and return all payments
        return [...pendingPayments, ...overduePayments, ...partiallyPaidPayments];
      },
      { tenantId }
    );
  }

  /**
   * Get all active athletes for template SMS
   */
  async getAllActiveAthletesForSms(
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any[]>> {
    return this.executeOperation(
      'getAllActiveAthletesForSms',
      async () => {
        const effectiveTenantId = tenantId || await getServerTenantId();

        // Get all active athletes with contact details
        return await TenantAwareDB.getAllAthletesWithContactDetails(effectiveTenantId || undefined);
      },
      { tenantId }
    );
  }

  /**
   * Send payment reminder SMS
   */
  async sendPaymentReminderSms(
    params: SendPaymentReminderSmsParams,
    userId?: string,
    tenantId?: string,
    language: string = 'en'
  ): Promise<ServiceResult<SmsServiceResult>> {
    return this.executeOperation(
      'sendPaymentReminderSms',
      async () => {
        const effectiveUserId = userId || await getServerUserId();
        const effectiveTenantId = tenantId || await getServerTenantId();

        // Get payments with athlete details
        const payments = await TenantAwareDB.getPaymentsByIdsWithAthleteDetails(
          params.paymentIds,
          effectiveTenantId || undefined
        );

        if (payments.length === 0) {
          throw new BusinessRuleError(
            'no_payments_found',
            'No payments found for the provided IDs'
          );
        }

        // Get template based on language and type
        let template = params.customTemplate;

        if (!template) {
          // For payment reminders, use language-specific templates from translation files
          // This ensures the correct language is used based on user's preference
          const templateKey = params.templateType === 'pending'
            ? 'configuration.templates.pending.default'
            : 'configuration.templates.overdue.default';

          // Import the server translation utility
          const { getServerTranslation } = await import('../utils/server-translation');
          template = getServerTranslation(templateKey, language, 'sms');

          // If translation not found, fallback to database configuration
          if (template === templateKey) {
            const configResult = await this.smsManagementService.getActiveSmsConfiguration(
              effectiveUserId?.toString(),
              effectiveTenantId || undefined
            );

            if (configResult.success) {
              const config = configResult.data;
              template = params.templateType === 'pending' ? config.pendingPaymentTemplate : config.overduePaymentTemplate;
            } else {
              throw new BusinessRuleError(
                'sms_config_error',
                'Failed to get SMS configuration and no translation found'
              );
            }
          }
        }

        // Ensure template is defined
        if (!template) {
          throw new BusinessRuleError(
            'template_not_found',
            'SMS template could not be loaded'
          );
        }

        // Calculate required SMS credits based on actual usage
        let totalRequiredCredits = 0;

        if (params.combinePayments) {
          // When combining payments, group by athlete and calculate credits per combined SMS
          const paymentsByAthlete = new Map<string, typeof payments>();

          for (const payment of payments) {
            if (!payment.athlete.parentPhone) {
              continue; // Skip if no phone number
            }

            const athleteId = payment.athleteId;
            if (!paymentsByAthlete.has(athleteId)) {
              paymentsByAthlete.set(athleteId, []);
            }
            paymentsByAthlete.get(athleteId)!.push(payment);
          }

          // Calculate credits for each combined SMS
          for (const [athleteId, athletePayments] of Array.from(paymentsByAthlete.entries())) {
            const firstPayment = athletePayments[0];
            // Calculate remaining amount for each payment (total - paid)
            const totalAmount = athletePayments.reduce((sum, p) => {
              const paymentTotal = parseFloat(p.amount);
              const paymentPaid = parseFloat(p.amountPaid || '0');
              return sum + (paymentTotal - paymentPaid); // Use remaining amount
            }, 0);

            const variables: SmsTemplateVariables = {
              schoolName: firstPayment.school?.name || '',
              teamName: firstPayment.team?.name || '',
              athleteName: `${firstPayment.athlete.name} ${firstPayment.athlete.surname}`,
              parentName: firstPayment.athlete.parentName || '',
              amount: `${totalAmount.toFixed(2)} TL`,
            };

            const processedMessage = this.smsManagementService.processTemplate(template, variables);
            const creditsForThisSms = this.calculateActualCredits(processedMessage);
            totalRequiredCredits += creditsForThisSms;
          }
        } else {
          // When not combining, calculate credits for each individual SMS
          for (const payment of payments) {
            if (!payment.athlete.parentPhone) {
              continue; // Skip if no phone number
            }

            // Calculate remaining amount (total - paid)
            const paymentTotal = parseFloat(payment.amount);
            const paymentPaid = parseFloat(payment.amountPaid || '0');
            const remainingAmount = paymentTotal - paymentPaid;

            const variables: SmsTemplateVariables = {
              schoolName: payment.school?.name || '',
              teamName: payment.team?.name || '',
              athleteName: `${payment.athlete.name} ${payment.athlete.surname}`,
              parentName: payment.athlete.parentName || '',
              amount: `${remainingAmount.toFixed(2)} TL`,
              paymentDueDate: new Date(payment.dueDate).toLocaleDateString(),
            };

            const processedMessage = this.smsManagementService.processTemplate(template, variables);
            const creditsForThisSms = this.calculateActualCredits(processedMessage);
            totalRequiredCredits += creditsForThisSms;
          }
        }

        // Check SMS balance with accurate credit calculation
        await this.checkAndValidateSmsBalance(
          totalRequiredCredits,
          effectiveUserId?.toString(),
          effectiveTenantId || undefined
        );

        // Prepare SMS messages
        const messages: SmsMessage[] = [];
        const logPromises: Promise<any>[] = [];
        let totalCreditsUsed = 0;

        if (params.combinePayments) {
          // Group payments by athlete when combining
          const paymentsByAthlete = new Map<string, typeof payments>();

          for (const payment of payments) {
            if (!payment.athlete.parentPhone) {
              continue; // Skip if no phone number
            }

            const athleteId = payment.athleteId;
            if (!paymentsByAthlete.has(athleteId)) {
              paymentsByAthlete.set(athleteId, []);
            }
            paymentsByAthlete.get(athleteId)!.push(payment);
          }

          // Send one SMS per athlete with combined payment information
          for (const [athleteId, athletePayments] of Array.from(paymentsByAthlete.entries())) {
            const firstPayment = athletePayments[0];
            // Calculate remaining amount for each payment (total - paid)
            const totalAmount = athletePayments.reduce((sum, p) => {
              const paymentTotal = parseFloat(p.amount);
              const paymentPaid = parseFloat(p.amountPaid || '0');
              return sum + (paymentTotal - paymentPaid); // Use remaining amount
            }, 0);

            const variables: SmsTemplateVariables = {
              schoolName: firstPayment.school?.name || '',
              teamName: firstPayment.team?.name || '',
              athleteName: `${firstPayment.athlete.name} ${firstPayment.athlete.surname}`,
              parentName: firstPayment.athlete.parentName || '',
              amount: `${totalAmount.toFixed(2)} TL`,
              // Don't include paymentDueDate for combined payments as they may have different due dates
            };

            const processedMessage = this.smsManagementService.processTemplate(template, variables);
            const actualCredits = this.calculateActualCredits(processedMessage);
            totalCreditsUsed += actualCredits;

            messages.push({
              receiver: firstPayment.athlete.parentPhone,
              message: processedMessage,
            });

            // Create one log entry for the combined SMS
            const smsLogResult = await this.smsLoggingService.createSmsLog(
              {
                type: 'payment_reminder',
                status: 'pending',
                receiver: firstPayment.athlete.parentPhone,
                message: processedMessage,
                senderIdentifier: this.config.defaultSender || 'SPORTSCLUB',
                creditsUsed: actualCredits,
                athleteId: firstPayment.athlete.id,
                teamId: firstPayment.team?.id,
                senderType: 'user',
                senderId: effectiveUserId?.toString(),
              },
              effectiveUserId?.toString(),
              effectiveTenantId || undefined
            );

            // Create payment relations for all payments in this group
            if (smsLogResult.success && smsLogResult.data) {
              const smsLogId = smsLogResult.data.id;
              for (const payment of athletePayments) {
                logPromises.push(
                  this.smsLoggingService.createSmsLogPaymentRelation(
                    smsLogId,
                    payment.id,
                    effectiveUserId?.toString(),
                    effectiveTenantId || undefined
                  )
                );
              }
            }
          }
        } else {
          // Send individual SMS for each payment (original logic)
          for (const payment of payments) {
            if (!payment.athlete.parentPhone) {
              continue; // Skip if no phone number
            }

            // Calculate remaining amount (total - paid)
            const paymentTotal = parseFloat(payment.amount);
            const paymentPaid = parseFloat(payment.amountPaid || '0');
            const remainingAmount = paymentTotal - paymentPaid;

            const variables: SmsTemplateVariables = {
              schoolName: payment.school?.name || '',
              teamName: payment.team?.name || '',
              athleteName: `${payment.athlete.name} ${payment.athlete.surname}`,
              parentName: payment.athlete.parentName || '',
              amount: `${remainingAmount.toFixed(2)} TL`,
              paymentDueDate: new Date(payment.dueDate).toLocaleDateString(),
            };

            const processedMessage = this.smsManagementService.processTemplate(template, variables);
            const actualCredits = this.calculateActualCredits(processedMessage);
            totalCreditsUsed += actualCredits;

            messages.push({
              receiver: payment.athlete.parentPhone,
              message: processedMessage,
            });

            // Create log entry for individual SMS
            const smsLogResult = await this.smsLoggingService.createSmsLog(
              {
                type: 'payment_reminder',
                status: 'pending',
                receiver: payment.athlete.parentPhone,
                message: processedMessage,
                senderIdentifier: this.config.defaultSender || 'SPORTSCLUB',
                creditsUsed: actualCredits,
                athleteId: payment.athlete.id,
                teamId: payment.team?.id,
                senderType: 'user',
                senderId: effectiveUserId?.toString(),
              },
              effectiveUserId?.toString(),
              effectiveTenantId || undefined
            );

            // Create payment relation for this SMS
            if (smsLogResult.success && smsLogResult.data) {
              logPromises.push(
                this.smsLoggingService.createSmsLogPaymentRelation(
                  smsLogResult.data.id,
                  payment.id,
                  effectiveUserId?.toString(),
                  effectiveTenantId || undefined
                )
              );
            }
          }
        }

        if (messages.length === 0) {
          throw new BusinessRuleError(
            'no_valid_recipients',
            'No valid phone numbers found for the selected payments'
          );
        }

        // Create log entries
        const logResults = await Promise.all(logPromises);
        const logIds = logResults.map(result => result.success ? result.data.id : null).filter(Boolean);

        // Send SMS messages
        const smsResult = await this.sendSms(
          {
            messages,
            metadata: {
              type: 'payment_reminder',
              templateType: params.templateType,
              paymentIds: params.paymentIds,
            }
          },
          effectiveUserId?.toString(),
          effectiveTenantId || undefined
        );

        if (smsResult.success) {

          // Deduct balance using actual credits used
          await this.smsBalanceService.deductSmsCredits(
            totalCreditsUsed,
            effectiveUserId?.toString(),
            effectiveTenantId || undefined
          );

          // Update log entries to success
          for (const logId of logIds) {
            if (logId) {
              await this.smsLoggingService.logSmsSuccess(
                logId,
                undefined,
                'SMS sent successfully',
                effectiveUserId?.toString(),
                effectiveTenantId || undefined
              );
            }
          }
        } else {
          // Update log entries to failed
          for (const logId of logIds) {
            if (logId) {
              await this.smsLoggingService.logSmsFailure(
                logId,
                smsResult.error?.message || 'SMS sending failed',
                effectiveUserId?.toString(),
                effectiveTenantId || undefined
              );
            }
          }
        }

        return smsResult.data || {
          success: false,
          sentCount: 0,
          failedCount: messages.length,
          totalCount: messages.length,
          messageIds: [],
        };
      },
      {
        userId,
        tenantId,
        resource: 'sms',
        metadata: { operation: 'payment_reminder', paymentIds: params.paymentIds },
      }
    );
  }

  /**
   * Send team SMS
   */
  async sendTeamSms(
    params: SendTeamSmsParams,
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<SmsServiceResult>> {
    return this.executeOperation(
      'sendTeamSms',
      async () => {
        const effectiveUserId = userId || await getServerUserId();
        const effectiveTenantId = tenantId || await getServerTenantId();

        // Get athletes for the team
        let athletes;
        if (params.athleteIds && params.athleteIds.length > 0) {
          // Get specific athletes
          athletes = await TenantAwareDB.getAthletesByIdsWithContactDetails(
            params.athleteIds,
            effectiveTenantId || undefined
          );
        } else {
          // Get all athletes in the team
          athletes = await TenantAwareDB.getAthletesByTeamIdWithContactDetails(
            params.teamId,
            effectiveTenantId || undefined
          );
        }

        if (athletes.length === 0) {
          throw new BusinessRuleError(
            'no_athletes_found',
            'No athletes found for the specified team'
          );
        }

        // Filter athletes with valid phone numbers
        const validAthletes = athletes.filter(athlete => athlete.parentPhone);

        if (validAthletes.length === 0) {
          throw new BusinessRuleError(
            'no_valid_recipients',
            'No valid phone numbers found for the selected athletes'
          );
        }

        // Calculate required SMS credits based on actual message length
        const creditsPerSms = this.calculateActualCredits(params.message);
        const totalRequiredCredits = validAthletes.length * creditsPerSms;

        // Check SMS balance with detailed context
        await this.checkAndValidateSmsBalance(
          totalRequiredCredits,
          effectiveUserId?.toString(),
          effectiveTenantId || undefined,
          `(${validAthletes.length} recipients × ${creditsPerSms} credits)`
        );

        // Prepare SMS messages
        const messages: SmsMessage[] = [];
        const logPromises: Promise<any>[] = [];

        for (const athlete of validAthletes) {
          messages.push({
            receiver: athlete.parentPhone,
            message: params.message,
          });

          // Create log entry
          logPromises.push(
            this.smsLoggingService.logSmsSending(
              'team_message',
              athlete.parentPhone,
              params.message,
              this.config.defaultSender || 'SPORTSCLUB',
              'user',
              effectiveUserId?.toString(),
              athlete.id,
              undefined,
              params.teamId,
              effectiveUserId?.toString(),
              effectiveTenantId || undefined
            )
          );
        }

        // Create log entries
        const logResults = await Promise.all(logPromises);
        const logIds = logResults.map(result => result.success ? result.data.id : null).filter(Boolean);

        // Send SMS messages
        const smsResult = await this.sendSms(
          {
            messages,
            metadata: {
              type: 'team_message',
              teamId: params.teamId,
              athleteIds: params.athleteIds,
            }
          },
          effectiveUserId?.toString(),
          effectiveTenantId || undefined
        );

        if (smsResult.success) {

          // Deduct balance using actual credits used
          await this.smsBalanceService.deductSmsCredits(
            totalRequiredCredits,
            effectiveUserId?.toString(),
            effectiveTenantId || undefined
          );

          // Update log entries to success
          for (const logId of logIds) {
            if (logId) {
              await this.smsLoggingService.logSmsSuccess(
                logId,
                undefined,
                'SMS sent successfully',
                effectiveUserId?.toString(),
                effectiveTenantId || undefined
              );
            }
          }
        } else {
          // Update log entries to failed
          for (const logId of logIds) {
            if (logId) {
              await this.smsLoggingService.logSmsFailure(
                logId,
                smsResult.error?.message || 'SMS sending failed',
                effectiveUserId?.toString(),
                effectiveTenantId || undefined
              );
            }
          }
        }

        return smsResult.data || {
          success: false,
          sentCount: 0,
          failedCount: messages.length,
          totalCount: messages.length,
          messageIds: [],
        };
      },
      {
        userId,
        tenantId,
        resource: 'sms',
        metadata: { operation: 'team_message', teamId: params.teamId },
      }
    );
  }

  /**
   * Get SMS template by type and language
   */
  async getSmsTemplateByType(
    type: 'general' | 'event' | 'meeting' | 'holiday' | 'custom',
    language: string = 'en',
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any>> {
    return this.executeOperation(
      'getSmsTemplateByType',
      async () => {
        console.log(`🔍 Service: Getting SMS template`, {
          type,
          language
        });

        const template = await TenantAwareDB.getSmsTemplateByType(
          type,
          language
        );

        console.log(`🔍 Service: Template result`, {
          type,
          language,
          found: !!template,
          template: template ? template.template.substring(0, 50) + '...' : null
        });

        return template;
      },
      {
        userId,
        tenantId,
        resource: 'sms_template',
        metadata: { operation: 'get_template_by_type', type, language },
      }
    );
  }

  /**
   * Send template SMS to selected athletes
   */
  async sendTemplateSms(
    params: SendTemplateSmsParams,
    userId?: string,
    tenantId?: string,
    language: string = 'en'
  ): Promise<ServiceResult<SmsServiceResult>> {
    return this.executeOperation(
      'sendTemplateSms',
      async () => {
        const effectiveUserId = userId || await getServerUserId();
        const effectiveTenantId = tenantId || await getServerTenantId();

        // Get athletes with contact details using proper service method
        const athletesResult = await this.getAllActiveAthletesForSms(
          effectiveUserId?.toString(),
          effectiveTenantId || undefined
        );

        if (!athletesResult.success) {
          throw new BusinessRuleError(
            'failed_to_get_athletes',
            'Failed to get athletes for SMS sending'
          );
        }

        if (!athletesResult.data) {
          throw new BusinessRuleError(
            'athletes_not_found',
            'Athletes data not found'
          );
        }

        // Filter to selected athletes with valid phone numbers
        const validAthletes = athletesResult.data.filter(athlete =>
          params.athleteIds.includes(athlete.id) && athlete.parentPhone
        );

        if (validAthletes.length === 0) {
          throw new BusinessRuleError(
            'no_valid_recipients',
            'No valid phone numbers found for the selected athletes'
          );
        }

        // Get template content
        let template = params.customTemplate;

        if (!template && params.templateType !== 'custom') {
          // Get template from database using service method
          const templateResult = await this.getSmsTemplateByType(
            params.templateType,
            language,
            effectiveUserId?.toString(),
            effectiveTenantId || undefined
          );

          if (templateResult.success && templateResult.data) {
            template = templateResult.data.template;
          } else {
            // Fallback to English template if current language not found
            const fallbackResult = await this.getSmsTemplateByType(
              params.templateType,
              'en',
              effectiveUserId?.toString(),
              effectiveTenantId || undefined
            );

            if (fallbackResult.success && fallbackResult.data) {
              template = fallbackResult.data.template;
            } else {
              throw new BusinessRuleError(
                'template_not_found',
                `No template found for type: ${params.templateType}`
              );
            }
          }
        }

        if (!template) {
          throw new BusinessRuleError(
            'no_template_content',
            'No template content provided'
          );
        }

        // Prepare SMS messages and calculate actual credits
        const messages: SmsMessage[] = [];
        const logPromises: Promise<any>[] = [];
        let totalRequiredCredits = 0;

        for (const athlete of validAthletes) {
          const variables: SmsTemplateVariables = {
            schoolName: '', // Template SMS doesn't have school context
            teamName: athlete.teamName || '',
            athleteName: `${athlete.name} ${athlete.surname}`,
            parentName: athlete.parentName || '',
            amount: '', // Template SMS doesn't have amount context
          };

          const processedMessage = this.smsManagementService.processTemplate(template, variables);
          const actualCredits = this.calculateActualCredits(processedMessage);
          totalRequiredCredits += actualCredits;

          messages.push({
            receiver: athlete.parentPhone,
            message: processedMessage,
          });

          // Create log entry for this SMS
          const smsLogResult = await this.smsLoggingService.createSmsLog(
            {
              type: 'custom',
              status: 'pending',
              receiver: athlete.parentPhone,
              message: processedMessage,
              senderIdentifier: this.config.defaultSender || 'SPORTSCLUB',
              creditsUsed: actualCredits,
              athleteId: athlete.id,
              senderType: 'user',
              senderId: effectiveUserId?.toString(),
            },
            effectiveUserId?.toString(),
            effectiveTenantId || undefined
          );

          if (smsLogResult.success) {
            logPromises.push(Promise.resolve(smsLogResult));
          }
        }

        if (messages.length === 0) {
          throw new BusinessRuleError(
            'no_valid_recipients',
            'No valid phone numbers found for the selected athletes'
          );
        }

        // Check SMS balance with actual calculated credits
        await this.checkAndValidateSmsBalance(
          totalRequiredCredits,
          effectiveUserId?.toString(),
          effectiveTenantId || undefined,
          `(${validAthletes.length} recipients, ${totalRequiredCredits} total credits)`
        );

        // Send SMS messages
        const smsResult = await this.sendSms(
          {
            messages,
            metadata: {
              type: 'template_message',
              templateType: params.templateType,
              athleteIds: params.athleteIds,
            }
          },
          effectiveUserId?.toString(),
          effectiveTenantId || undefined
        );

        if (smsResult.success) {

          // Deduct balance using actual credits used
          await this.smsBalanceService.deductSmsCredits(
            totalRequiredCredits,
            effectiveUserId?.toString(),
            effectiveTenantId || undefined
          );

          // Update log entries to success
          const logResults = await Promise.all(logPromises);
          for (const logResult of logResults) {
            if (logResult.success && logResult.data) {
              await this.smsLoggingService.logSmsSuccess(
                logResult.data.id,
                logResult.data.creditsUsed,
                'SMS sent successfully',
                effectiveUserId?.toString(),
                effectiveTenantId || undefined
              );
            }
          }
        } else {
          // Update log entries to failed
          const logResults = await Promise.all(logPromises);
          for (const logResult of logResults) {
            if (logResult.success && logResult.data) {
              await this.smsLoggingService.logSmsFailure(
                logResult.data.id,
                smsResult.error?.message || 'SMS sending failed',
                effectiveUserId?.toString(),
                effectiveTenantId || undefined
              );
            }
          }
        }

        return smsResult.data || {
          success: false,
          sentCount: 0,
          failedCount: messages.length,
          totalCount: messages.length,
          messageIds: [],
        };
      },
      {
        userId,
        tenantId,
        resource: 'sms',
        metadata: { operation: 'template_message', templateType: params.templateType },
      }
    );
  }
}
