import { BaseService } from './base';
import { TenantAwareDB } from '../db';
import { ServiceResult } from '../errors/types';
import { calculateAthleteBalance, updateAthleteBalance } from '../balance-calculator';

export class BalanceProcessorService extends BaseService {
  constructor() {
    super('BalanceProcessorService');
  }

  /**
   * Calculate athlete balance excluding a specific payment from outstanding payments
   * This prevents circular dependency when processing balance allocation for a newly created payment
   */
  private async calculateAthleteBalanceExcludingPayment(athleteId: string, tenantId: string, excludePaymentId: string): Promise<number> {
    // Get all balance top-up transactions (paymentId is null - these add to balance)
    const totalCredits = await TenantAwareDB.getAthleteBalanceTopUpsSum(athleteId, tenantId);

    // Get all balance usage transactions (existing_balance method - these subtract from balance)
    const totalUsage = await TenantAwareDB.getAthleteBalanceUsageSum(athleteId, tenantId);

    // Get all outstanding payments (pending, overdue, partially_paid) EXCLUDING the current payment
    const totalOutstanding = await TenantAwareDB.getAthleteOutstandingPaymentsSum(athleteId, tenantId, excludePaymentId);

    // Return balance: credits - usage - outstanding = available balance
    return totalCredits - totalUsage - totalOutstanding;
  }

  /**
   * Process automatic balance allocation for a new payment
   * This is called when a new payment is created to check if athlete has sufficient balance
   */
  async processNewPaymentBalanceAllocation(
    paymentId: string,
    tenantId?: string,
    userId?: string,
    effectiveTenantId?: string
  ): Promise<ServiceResult<any>> {
    return this.executeDatabaseOperation(
      'processNewPaymentBalanceAllocation',
      async () => {
        const resolvedTenantId = effectiveTenantId || tenantId;
        const userIdBigInt = userId ? BigInt(userId) : undefined;

        // Get the payment details
        const payment = await TenantAwareDB.getPaymentById(paymentId, resolvedTenantId);
        if (!payment) {
          throw new Error('Payment not found');
        }

        // Get athlete's current balance (positive = credit, negative = debt)
        // Exclude the current payment from outstanding payments calculation to avoid circular dependency
        const athleteBalance = await this.calculateAthleteBalanceExcludingPayment(payment.athleteId, resolvedTenantId!, paymentId);

        // Only process if athlete has positive balance
        if (athleteBalance <= 0) {
          return {
            allocated: false,
            message: 'No positive balance available for allocation',
            balance: athleteBalance
          };
        }

        const paymentAmount = parseFloat(payment.amount);
        const currentAmountPaid = parseFloat(payment.amountPaid || '0');
        const remainingAmount = paymentAmount - currentAmountPaid;

        // If payment is already fully paid, nothing to do
        if (remainingAmount <= 0) {
          return { allocated: false, balance: athleteBalance, reason: 'Payment already fully paid' };
        }

        // Calculate how much balance to use
        const balanceToUse = Math.min(athleteBalance, remainingAmount);
        
        // Skip if balanceToUse is 0 to avoid creating 0-amount transactions
        if (balanceToUse <= 0) {
          return { 
            allocated: false, 
            balance: athleteBalance, 
            reason: 'No balance available to allocate' 
          };
        }
        
        const newAmountPaid = currentAmountPaid + balanceToUse;

        // Determine new payment status
        let newStatus: 'pending' | 'completed' | 'overdue' | 'cancelled' | 'partially_paid';
        if (newAmountPaid >= paymentAmount) {
          newStatus = 'completed';
        } else if (newAmountPaid > 0) {
          newStatus = 'partially_paid';
        } else {
          newStatus = payment.status; // Keep current status
        }

                // Create a transaction for the balance allocation
        await TenantAwareDB.createPaymentTransaction({
          athleteId: payment.athleteId,
          paymentId: paymentId,
          amount: balanceToUse.toFixed(2),
          transactionMethod: 'existing_balance',
          notes: `balance_allocation:${payment.description || payment.type || 'Monthly Fee'}`,
        }, resolvedTenantId, userIdBigInt);

        // Update payment with new amount paid and status
        await TenantAwareDB.updatePayment(paymentId, {
          amountPaid: newAmountPaid.toFixed(2),
          status: newStatus,
        }, resolvedTenantId, userIdBigInt);

        // Update athlete balance in database
        await updateAthleteBalance(payment.athleteId, resolvedTenantId!, userIdBigInt);

        return {
          allocated: true,
          balanceUsed: balanceToUse,
          newAmountPaid,
          newStatus,
          remainingBalance: athleteBalance - balanceToUse,
        };
      },
      { tenantId: effectiveTenantId || tenantId, userId }
    );
  }

  /**
   * Process balance allocation for all pending payments of an athlete
   * This is called when athlete receives a balance top-up
   */
  async processAthleteBalanceAllocation(
    athleteId: string,
    tenantId?: string,
    userId?: string,
    effectiveTenantId?: string,
    addedAmount?: number
  ): Promise<ServiceResult<any>> {
    return this.executeDatabaseOperation(
      'processAthleteBalanceAllocation',
      async () => {
        const resolvedTenantId = effectiveTenantId || tenantId;
        const userIdBigInt = userId ? BigInt(userId) : undefined;

        // Use the added amount as available balance to allocate
        // If no amount specified, get the latest balance top-up transaction
        let remainingBalance: number;
        let originalAmount: number;

        if (addedAmount !== undefined) {
          remainingBalance = addedAmount;
          originalAmount = addedAmount;
        } else {
          const latestTopUp = await TenantAwareDB.getPaymentTransactionsPaginated(
            1, 1, undefined, 'transactionDate', 'desc',
            { athleteId, paymentId: 'null' }, // paymentId is null for balance top-ups
            resolvedTenantId
          );

          if (!latestTopUp.data || latestTopUp.data.length === 0) {
            return {
              allocated: false,
              message: 'No recent balance top-up found'
            };
          }

          remainingBalance = parseFloat(latestTopUp.data[0].amount);
          originalAmount = remainingBalance;
        }

        // Get all payments for this athlete and filter manually for multiple statuses
        const allPayments = await TenantAwareDB.getPaymentsPaginated(
          1,
          1000, // Get all payments for this athlete
          undefined,
          'dueDate',
          'asc',
          {
            athleteId: athleteId
          },
          resolvedTenantId
        );

        // Filter for pending, partially_paid, and overdue payments
        const eligiblePayments = (allPayments.data || []).filter(p =>
          ['pending', 'partially_paid', 'overdue'].includes(p.status)
        );

        // PRIORITY ORDER: Process payments by priority
        // 1. Overdue & partially paid (least remaining amount first)
        // 2. Overdue & not paid (most recent due date first, then least remaining amount)  
        // 3. Pending payments (most recent due date first, then least remaining amount)
        
        const overduePartiallyPaid = [];
        const overdueNotPaid = [];
        const pendingPayments = [];

        for (const payment of eligiblePayments) {
          const paymentAmount = parseFloat(payment.amount);
          const currentAmountPaid = parseFloat(payment.amountPaid || '0');
          const remainingAmount = paymentAmount - currentAmountPaid;

          if (remainingAmount <= 0) continue; // Skip fully paid payments

          const paymentWithRemaining = {
            ...payment,
            remainingAmount
          };

          if (payment.status === 'overdue') {
            if (currentAmountPaid > 0) {
              overduePartiallyPaid.push(paymentWithRemaining);
            } else {
              overdueNotPaid.push(paymentWithRemaining);
            }
          } else if (payment.status === 'pending') {
            pendingPayments.push(paymentWithRemaining);
          } else if (payment.status === 'partially_paid') {
            // Treat partially_paid as overdue if past due date
            const dueDate = new Date(payment.dueDate);
            const today = new Date();
            if (dueDate < today) {
              overduePartiallyPaid.push(paymentWithRemaining);
            } else {
              pendingPayments.push(paymentWithRemaining);
            }
          }
        }

        // Sort each group by priority:
        // For overdue partially paid: by remaining amount (ascending - smallest first)
        overduePartiallyPaid.sort((a, b) => a.remainingAmount - b.remainingAmount);
        
        // For overdue not paid: by due date (descending - most recent first), then by remaining amount
        overdueNotPaid.sort((a, b) => {
          const dateComparison = new Date(b.dueDate).getTime() - new Date(a.dueDate).getTime();
          if (dateComparison !== 0) return dateComparison;
          return a.remainingAmount - b.remainingAmount;
        });
        
        // For pending payments: by due date (descending - most recent first), then by remaining amount
        pendingPayments.sort((a, b) => {
          const dateComparison = new Date(b.dueDate).getTime() - new Date(a.dueDate).getTime();
          if (dateComparison !== 0) return dateComparison;
          return a.remainingAmount - b.remainingAmount;
        });

        // Create prioritized payment list
        const prioritizedPayments = [
          ...overduePartiallyPaid,
          ...overdueNotPaid,
          ...pendingPayments
        ];

        // Log the prioritization for debugging
        console.log(`Balance allocation priority order for athlete ${athleteId}:`, {
          overduePartiallyPaid: overduePartiallyPaid.length,
          overdueNotPaid: overdueNotPaid.length,
          pendingPayments: pendingPayments.length,
          totalPrioritized: prioritizedPayments.length,
          availableBalance: remainingBalance
        });

        const allocations = [];
        // remainingBalance is already set above from the latest top-up amount

        for (const payment of prioritizedPayments) {
          if (remainingBalance <= 0) break;

          const paymentAmount = parseFloat(payment.amount);
          const currentAmountPaid = parseFloat(payment.amountPaid || '0');
          const remainingPaymentAmount = paymentAmount - currentAmountPaid;

          if (remainingPaymentAmount <= 0) continue; // Skip fully paid payments

          // Calculate how much balance to use for this payment
          const balanceToUse = Math.min(remainingBalance, remainingPaymentAmount);
          
          // Skip if balanceToUse is 0 to avoid creating 0-amount transactions
          if (balanceToUse <= 0) continue;
          
          const newAmountPaid = currentAmountPaid + balanceToUse;

          // Determine new payment status
          let newStatus: 'pending' | 'completed' | 'overdue' | 'cancelled' | 'partially_paid';
          if (newAmountPaid >= paymentAmount) {
            newStatus = 'completed';
          } else if (newAmountPaid > 0) {
            newStatus = 'partially_paid';
          } else {
            newStatus = payment.status; // Keep current status
          }

          // Log each payment processing for debugging
          console.log(`Processing payment ${payment.id}:`, {
            paymentAmount,
            currentAmountPaid,
            remainingPaymentAmount,
            balanceToUse,
            newAmountPaid,
            newStatus,
            remainingBalance: remainingBalance - balanceToUse
          });

          // Create transaction for balance usage
          await TenantAwareDB.createPaymentTransaction({
            athleteId: athleteId,
            paymentId: payment.id,
            amount: balanceToUse.toFixed(2),
            transactionMethod: 'existing_balance',
            notes: `balance_allocation:${payment.description || payment.type || 'Payment'}`,
          }, resolvedTenantId, userIdBigInt);

          // Update payment
          await TenantAwareDB.updatePayment(payment.id, {
            amountPaid: newAmountPaid.toFixed(2),
            status: newStatus,
          }, resolvedTenantId, userIdBigInt);

          allocations.push({
            paymentId: payment.id,
            balanceUsed: balanceToUse,
            newAmountPaid,
            newStatus,
          });

          remainingBalance -= balanceToUse;
        }

        // Update athlete balance in database
        await updateAthleteBalance(athleteId, resolvedTenantId!, userIdBigInt);

        const totalBalanceUsed = originalAmount - remainingBalance;

        return {
          allocated: allocations.length > 0,
          allocations,
          totalBalanceUsed,
          remainingBalance,
        };
      },
      { tenantId: effectiveTenantId || tenantId, userId }
    );
  }

  /**
   * Get athlete balance summary with breakdown
   */
  async getAthleteBalanceSummary(
    athleteId: string,
    tenantId?: string,
    effectiveTenantId?: string
  ): Promise<ServiceResult<any>> {
    return this.executeDatabaseOperation(
      'getAthleteBalanceSummary',
      async () => {
        const resolvedTenantId = effectiveTenantId || tenantId;

        // Get current balance
        const currentBalance = await calculateAthleteBalance(athleteId, resolvedTenantId!);

        // Get balance top-up transactions
        const balanceTransactions = await TenantAwareDB.getPaymentTransactionsPaginated(
          1,
          50,
          undefined,
          'transactionDate',
          'desc',
          { athleteId, paymentId: 'null' }, // paymentId is null for balance top-ups
          resolvedTenantId
        );

        // Get pending payments
        const pendingPayments = await TenantAwareDB.getPaymentsPaginated(
          1,
          50,
          undefined,
          'dueDate',
          'asc',
          { 
            status: 'pending,partially_paid,overdue',
            athleteId: athleteId 
          },
          resolvedTenantId
        );

        return {
          currentBalance,
          balanceTransactions: balanceTransactions.data || [],
          pendingPayments: pendingPayments.data || [],
          hasPositiveBalance: currentBalance > 0,
          hasOutstandingPayments: (pendingPayments.data || []).length > 0,
        };
      },
      { tenantId: effectiveTenantId || tenantId }
    );
  }
}

// Export singleton instance
let balanceProcessorServiceInstance: BalanceProcessorService | null = null;

export function balanceProcessorService(): BalanceProcessorService {
  if (!balanceProcessorServiceInstance) {
    balanceProcessorServiceInstance = new BalanceProcessorService();
  }
  return balanceProcessorServiceInstance;
}
