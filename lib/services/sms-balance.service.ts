import { BaseService } from './base';
import { ServiceResult } from '../errors/types';
import { BusinessRuleError } from '../errors/errors';
import { TenantAwareDB } from '../db';
import { getServerTenantId, getServerUserId } from '../tenant-utils-server';
import logger from '@/lib/logger';

/**
 * SMS Balance Service
 * Handles SMS balance management and tracking
 */
export class SmsBalanceService extends BaseService {
  constructor() {
    super('SmsBalanceService');
  }

  /**
   * Get SMS balance for tenant
   */
  async getSmsBalance(
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any>> {
    return this.executeOperation(
      'getSmsBalance',
      async () => {
        const effectiveTenantId = tenantId || await getServerTenantId();
        logger.info('🔍 SMS Balance Service - Getting balance for tenant:', effectiveTenantId);

        let balance = await TenantAwareDB.getSmsBalance(effectiveTenantId || undefined);

        logger.info('📊 SMS Balance Service - Raw balance result:', balance);

        // Initialize balance if it doesn't exist
        if (!balance) {
          logger.info('Initializing balance...');
          const effectiveUserId = userId || await getServerUserId();
          balance = await TenantAwareDB.initializeSmsBalance(
            effectiveTenantId || undefined,
            effectiveUserId ? BigInt(effectiveUserId) : undefined
          );
          logger.info('✅ SMS Balance Service - Initialized balance:', balance);
        }

        return balance;
      },
      {
        userId,
        tenantId,
        resource: 'sms_balance',
      }
    );
  }

  /**
   * Add SMS credits to balance
   */
  async addSmsCredits(
    amount: number,
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any>> {
    return this.executeOperation(
      'addSmsCredits',
      async () => {
        const effectiveUserId = userId || await getServerUserId();
        const effectiveTenantId = tenantId || await getServerTenantId();

        if (amount <= 0) {
          throw new BusinessRuleError(
            'invalid_amount',
            'SMS credit amount must be positive'
          );
        }

        return TenantAwareDB.incrementSmsBalance(
          amount,
          effectiveTenantId || undefined,
          effectiveUserId ? BigInt(effectiveUserId) : undefined
        );
      },
      {
        userId,
        tenantId,
        resource: 'sms_balance',
        metadata: { creditAmount: amount },
      }
    );
  }

  /**
   * Deduct SMS credits from balance
   */
  async deductSmsCredits(
    amount: number,
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any>> {
    return this.executeOperation(
      'deductSmsCredits',
      async () => {
        const effectiveUserId = userId || await getServerUserId();
        const effectiveTenantId = tenantId || await getServerTenantId();

        if (amount <= 0) {
          throw new BusinessRuleError(
            'invalid_amount',
            'SMS deduction amount must be positive'
          );
        }

        // Check if sufficient balance exists
        const hasSufficientBalance = await TenantAwareDB.checkSufficientSmsBalance(
          amount,
          effectiveTenantId || undefined
        );

        if (!hasSufficientBalance) {
          throw new BusinessRuleError(
            'insufficient_balance',
            'Insufficient SMS balance for this operation'
          );
        }

        return TenantAwareDB.decrementSmsBalance(
          amount,
          effectiveTenantId || undefined,
          effectiveUserId ? BigInt(effectiveUserId) : undefined
        );
      },
      {
        userId,
        tenantId,
        resource: 'sms_balance',
        metadata: { deductionAmount: amount },
      }
    );
  }

  /**
   * Check if sufficient balance exists for sending SMS
   */
  async checkSufficientBalance(
    requiredAmount: number,
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<boolean>> {
    return this.executeOperation(
      'checkSufficientBalance',
      async () => {
        const effectiveTenantId = tenantId || await getServerTenantId();
        return TenantAwareDB.checkSufficientSmsBalance(
          requiredAmount,
          effectiveTenantId || undefined
        );
      },
      {
        userId,
        tenantId,
        resource: 'sms_balance',
        metadata: { requiredAmount },
      }
    );
  }

  /**
   * Get balance status with warnings
   */
  async getBalanceStatus(
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<{
    balance: number;
    status: 'sufficient' | 'low' | 'empty';
  }>> {
    return this.executeOperation(
      'getBalanceStatus',
      async () => {
        const balanceResult = await this.getSmsBalance(userId, tenantId);
        if (!balanceResult.success) {
          throw new Error('Failed to get SMS balance');
        }

        const balance = balanceResult.data?.balance || 0;
        let status: 'sufficient' | 'low' | 'empty';

        if (balance === 0) {
          status = 'empty';
        } else if (balance <= 10) {
          status = 'low';
        } else {
          status = 'sufficient';
        }

        return {
          balance,
          status,
        };
      },
      {
        userId,
        tenantId,
        resource: 'sms_balance',
      }
    );
  }
}
