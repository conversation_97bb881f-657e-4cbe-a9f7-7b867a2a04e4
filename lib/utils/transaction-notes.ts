/**
 * Utility functions for formatting transaction notes with i18n support
 */

export function formatTransactionNotes(notes: string | null, t: (key: string, options?: any) => string): string {
  if (!notes) return '';
  
  // Handle special transaction note formats
  if (notes.startsWith('balance_allocation:')) {
    const paymentDescription = notes.replace('balance_allocation:', '');
    
    // Translate payment types for better readability
    let translatedDescription = paymentDescription;
    if (paymentDescription === 'fee') {
      translatedDescription = t('payments.types.fee');
    } else if (paymentDescription === 'equipment') {
      translatedDescription = t('payments.types.equipment');
    } else if (paymentDescription === 'other') {
      translatedDescription = t('payments.types.other');
    }
    
    return t('payments.transactions.notes.balanceAllocation', { payment: translatedDescription });
  }
  
  if (notes.startsWith('overpayment_credit:')) {
    const paymentDescription = notes.replace('overpayment_credit:', '');
    
    // Translate payment types for better readability
    let translatedDescription = paymentDescription;
    if (paymentDescription === 'fee') {
      translatedDescription = t('payments.types.fee');
    } else if (paymentDescription === 'equipment') {
      translatedDescription = t('payments.types.equipment');
    } else if (paymentDescription === 'other') {
      translatedDescription = t('payments.types.other');
    }
    
    return t('payments.transactions.notes.overpaymentCredit', { payment: translatedDescription });
  }

  if (notes.startsWith('completed_payment:')) {
    const paymentDescription = notes.replace('completed_payment:', '');
    
    // Translate payment types for better readability
    let translatedDescription = paymentDescription;
    if (paymentDescription === 'fee') {
      translatedDescription = t('payments.types.fee');
    } else if (paymentDescription === 'equipment') {
      translatedDescription = t('payments.types.equipment');
    } else if (paymentDescription === 'other') {
      translatedDescription = t('payments.types.other');
    }
    
    return t('payments.transactions.notes.completedPayment', { payment: translatedDescription });
  }

  if (notes.startsWith('refund:')) {
    const refundReason = notes.replace('refund:', '');
    return t('payments.transactions.notes.refund', { reason: refundReason });
  }
  
  // Handle other special cases
  if (notes === 'Balance top-up') {
    return t('payments.transactions.notes.balanceTopUp');
  }
  
  // Return original notes if no special format
  return notes;
}

export function isSystemGeneratedNote(notes: string | null): boolean {
  if (!notes) return false;
  
  return notes.startsWith('balance_allocation:') || 
         notes.startsWith('overpayment_credit:') ||
         notes === 'Balance top-up';
}
