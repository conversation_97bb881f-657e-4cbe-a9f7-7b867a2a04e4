/**
 * Payment Business Rules - Advanced Payment Record Management Logic
 * 
 * This module implements state-based action permissions for payment records,
 * ensuring data integrity and accurate financial tracking.
 * 
 * Core Principle: The status of a payment record is the single source of truth
 * for determining which actions a user can perform on it.
 */

import { Payment } from '@/lib/types';

export interface PaymentPermissions {
  canEdit: boolean;
  canDelete: boolean;
  canEditAmount: boolean;
  canIncreaseAmount: boolean;
  canDecreaseAmount: boolean;
  maxDecreaseAmount?: number;
  editRestrictions: {
    amountOnly?: boolean;
    fieldsAllowed?: string[];
    fieldsDisallowed?: string[];
  };
  rationale: string;
}

export interface AmountChangeValidation {
  isValid: boolean;
  maxDecrease?: number;
  currentUnpaidBalance?: number;
  errorKey?: string;
  errorParams?: Record<string, any>;
}

/**
 * Determines the permissions for a payment record based on its status and payment state
 */
export function getPaymentPermissions(payment: Payment): PaymentPermissions {
  const totalAmount = parseFloat(payment.amount);
  const paidAmount = parseFloat(payment.amountPaid || '0');
  const isPartiallyPaid = paidAmount > 0 && paidAmount < totalAmount;
  const unpaidBalance = totalAmount - paidAmount;

  switch (payment.status) {
    case 'pending':
      return {
        canEdit: true,
        canDelete: true,
        canEditAmount: true,
        canIncreaseAmount: true,
        canDecreaseAmount: true,
        editRestrictions: {},
        rationale: 'payments.businessRules.rationale.pending'
      };

    case 'overdue':
      if (!isPartiallyPaid) {
        return {
          canEdit: true,
          canDelete: true,
          canEditAmount: true,
          canIncreaseAmount: true,
          canDecreaseAmount: true,
          editRestrictions: {},
          rationale: 'payments.businessRules.rationale.overdueNoPay'
        };
      } else {
        return {
          canEdit: true,
          canDelete: false, // Cannot delete overdue payments with partial payments
          canEditAmount: true,
          canIncreaseAmount: true,
          canDecreaseAmount: true,
          maxDecreaseAmount: unpaidBalance,
          editRestrictions: {
            fieldsAllowed: ['amount', 'description', 'dueDate'],
            fieldsDisallowed: ['athleteId', 'date', 'type']
          },
          rationale: 'payments.businessRules.rationale.overduePartialPay'
        };
      }

    case 'completed':
      return {
        canEdit: false, // Completed payments cannot be edited
        canDelete: false, // Completed payments cannot be deleted
        canEditAmount: false,
        canIncreaseAmount: false,
        canDecreaseAmount: false,
        editRestrictions: {},
        rationale: 'payments.businessRules.rationale.completed'
      };

    case 'partially_paid':
      return {
        canEdit: true,
        canDelete: false, // Cannot delete partially paid payments
        canEditAmount: true,
        canIncreaseAmount: true,
        canDecreaseAmount: true,
        maxDecreaseAmount: unpaidBalance,
        editRestrictions: {
          fieldsAllowed: ['amount', 'description', 'dueDate'],
          fieldsDisallowed: ['athleteId', 'date', 'type'] // Critical fields cannot be changed after payment
        },
        rationale: 'payments.businessRules.rationale.partiallyPaid'
      };

    case 'cancelled':
      return {
        canEdit: false,
        canDelete: true,
        canEditAmount: false,
        canIncreaseAmount: false,
        canDecreaseAmount: false,
        editRestrictions: {},
        rationale: 'payments.businessRules.rationale.cancelled'
      };

    default:
      // Fallback to most restrictive permissions
      return {
        canEdit: false,
        canDelete: false,
        canEditAmount: false,
        canIncreaseAmount: false,
        canDecreaseAmount: false,
        editRestrictions: {},
        rationale: 'payments.businessRules.rationale.unknown'
      };
  }
}

/**
 * Validates if an amount change is allowed for a payment
 */
export function validateAmountChange(
  payment: Payment, 
  newAmount: number
): AmountChangeValidation {
  const permissions = getPaymentPermissions(payment);
  const currentAmount = parseFloat(payment.amount);
  const paidAmount = parseFloat(payment.amountPaid || '0');
  const unpaidBalance = currentAmount - paidAmount;
  const amountDifference = newAmount - currentAmount;

  // If amount editing is not allowed at all
  if (!permissions.canEditAmount) {
    return {
      isValid: false,
      errorKey: 'payments.businessRules.errors.amountEditNotAllowed'
    };
  }

  // If trying to increase amount and increases are not allowed
  if (amountDifference > 0 && !permissions.canIncreaseAmount) {
    return {
      isValid: false,
      errorKey: 'payments.businessRules.errors.amountIncreaseNotAllowed'
    };
  }

  // If trying to decrease amount and decreases are not allowed
  if (amountDifference < 0 && !permissions.canDecreaseAmount) {
    return {
      isValid: false,
      errorKey: 'payments.businessRules.errors.amountDecreaseNotAllowed'
    };
  }

  // If trying to decrease amount beyond the allowed limit
  if (amountDifference < 0 && permissions.maxDecreaseAmount !== undefined) {
    const decreaseAmount = Math.abs(amountDifference);
    if (decreaseAmount > permissions.maxDecreaseAmount) {
      return {
        isValid: false,
        maxDecrease: permissions.maxDecreaseAmount,
        currentUnpaidBalance: unpaidBalance,
        errorKey: 'payments.businessRules.errors.amountDecreaseTooLarge',
        errorParams: {
          maxDecrease: permissions.maxDecreaseAmount.toFixed(2),
          unpaidBalance: unpaidBalance.toFixed(2),
          attemptedDecrease: decreaseAmount.toFixed(2)
        }
      };
    }
  }

  // If new amount would be less than already paid amount
  if (newAmount < paidAmount) {
    return {
      isValid: false,
      errorKey: 'payments.businessRules.errors.amountLessThanPaid',
      errorParams: {
        paidAmount: paidAmount.toFixed(2),
        newAmount: newAmount.toFixed(2)
      }
    };
  }

  return {
    isValid: true,
    currentUnpaidBalance: unpaidBalance
  };
}

/**
 * Validates if a field can be edited for a payment
 */
export function canEditField(payment: Payment, fieldName: string): boolean {
  const permissions = getPaymentPermissions(payment);
  
  if (!permissions.canEdit) {
    return false;
  }

  const { editRestrictions } = permissions;

  // If there are specific fields allowed, check if this field is in the list
  if (editRestrictions.fieldsAllowed) {
    return editRestrictions.fieldsAllowed.includes(fieldName);
  }

  // If there are specific fields disallowed, check if this field is NOT in the list
  if (editRestrictions.fieldsDisallowed) {
    return !editRestrictions.fieldsDisallowed.includes(fieldName);
  }

  // Default: allow editing if general edit permission is granted
  return true;
}

/**
 * Business rule validation result type
 */
export interface BusinessRuleValidation {
  isValid: boolean;
  error?: string;
  errorKey?: string;
  rationale?: string;
  errorParams?: Record<string, any>;
}

/**
 * Server-side validator class for payment business rules
 */
export class PaymentBusinessRulesValidator {
  /**
   * Server-side validator for edit operations
   */
  static validateEdit(payment: Payment, data: any): BusinessRuleValidation {
    const permissions = getPaymentPermissions(payment);
    
    if (!permissions.canEdit) {
      return {
        isValid: false,
        error: 'PAYMENT_EDIT_NOT_ALLOWED',
        errorKey: 'payments.businessRules.errors.editNotAllowed',
        rationale: permissions.rationale || 'This payment cannot be edited in its current state'
      };
    }
    
    // Validate amount changes if amount is being updated
    if (data.amount !== undefined) {
      const newAmount = parseFloat(data.amount);
      const validation = validateAmountChange(payment, newAmount);
      
      if (!validation.isValid) {
        return {
          isValid: false,
          error: 'PAYMENT_AMOUNT_CHANGE_NOT_ALLOWED',
          errorKey: validation.errorKey,
          rationale: validation.errorParams ? 
            `${validation.errorKey} - ${JSON.stringify(validation.errorParams)}` : 
            validation.errorKey
        };
      }
    }
    
    return { isValid: true };
  }

  /**
   * Server-side validator for delete operations
   */
  static validateDelete(payment: Payment): BusinessRuleValidation {
    const permissions = getPaymentPermissions(payment);
    
    if (!permissions.canDelete) {
      return {
        isValid: false,
        error: 'PAYMENT_DELETE_NOT_ALLOWED',
        errorKey: 'payments.businessRules.errors.deleteNotAllowed',
        rationale: permissions.rationale || 'This payment cannot be deleted in its current state'
      };
    }
    
    return { isValid: true };
  }

  /**
   * Server-side validator for refund creation
   */
  static validateRefundCreation(payment: Payment): BusinessRuleValidation {
    const amountPaid = parseFloat(payment.amountPaid || '0');
    
    // Check if payment has been paid
    if (amountPaid <= 0) {
      return {
        isValid: false,
        error: 'PAYMENT_REFUND_NO_PAYMENT',
        errorKey: 'payments.businessRules.errors.refundNoPayment',
        rationale: 'Cannot create refund for a payment that has not received any payment.'
      };
    }
    
    return { isValid: true };
  }

  /**
   * Server-side validator for refund operations with amount validation
   */
  static validateRefund(payment: Payment, refundAmount: number): BusinessRuleValidation {
    const amountPaid = parseFloat(payment.amountPaid || '0');
    
    // Check if payment has been paid
    if (amountPaid <= 0) {
      return {
        isValid: false,
        error: 'PAYMENT_REFUND_NO_PAYMENT',
        errorKey: 'payments.businessRules.errors.refundNoPayment',
        rationale: 'Cannot create refund for a payment that has not received any payment.'
      };
    }
    
    // Check if refund amount is valid
    if (refundAmount <= 0) {
      return {
        isValid: false,
        error: 'PAYMENT_REFUND_INVALID_AMOUNT',
        errorKey: 'payments.businessRules.errors.refundInvalidAmount',
        rationale: 'Refund amount must be greater than zero.'
      };
    }
    
    // Check if refund amount doesn't exceed paid amount
    if (refundAmount > amountPaid) {
      return {
        isValid: false,
        error: 'PAYMENT_REFUND_EXCEEDS_PAID',
        errorKey: 'payments.businessRules.errors.refundExceedsPaid',
        rationale: `Refund amount (${refundAmount}) cannot exceed the total amount paid (${amountPaid}).`
      };
    }
    
    return { isValid: true };
  }
}
