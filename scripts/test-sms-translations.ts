#!/usr/bin/env tsx

import logger from '@/lib/logger';

/**
 * Test SMS Translation Variables Script
 * 
 * This script tests that SMS translation variables are properly replaced
 * to help debug the count variable issue.
 */

// Mock i18next for testing
const mockTranslations = {
  'sms:sending.paymentReminder.warningPlural': 'This will send Sms messages to {count} recipients and will consume {count} Sms credits from your balance.',
  'sms:sending.teamMessage.warningPlural': 'This will send Sms messages to {count} recipients and will consume {count} Sms credits from your balance.',
  'sms:sending.teamMessage.warningSmsCountSingle': 'Total: {total} Sms credit will be consumed from your balance.',
  'sms:sending.teamMessage.warningSmsCountPlural': 'Each recipient will receive {smsPerRecipient} Sms messages. Total: {total} Sms credits will be consumed from your balance.',
};

function mockT(key: string): string {
  return mockTranslations[key as keyof typeof mockTranslations] || key;
}

function testTranslationReplacement() {
  logger.info('🧪 Testing SMS Translation Variable Replacement');
  logger.info('==============================================\n');

  // Test 1: Payment Reminder Dialog
  logger.info('📋 Test 1: Payment Reminder Dialog');
  const paymentCount = 2;
  const paymentReminderMessage = mockT('sms:sending.paymentReminder.warningPlural')
    .replace(/{count}/g, paymentCount.toString());
  
  logger.info(`Input: ${mockT('sms:sending.paymentReminder.warningPlural')}`);
  logger.info(`Output: ${paymentReminderMessage}`);
  logger.info(`✅ Contains {count}: ${mockT('sms:sending.paymentReminder.warningPlural').includes('{count}')}`);
  logger.info(`✅ Replaced properly: ${!paymentReminderMessage.includes('{count}')}\n`);

  // Test 2: Team Message Dialog - Single SMS
  logger.info('📋 Test 2: Team Message Dialog - Single SMS');
  const recipientCount = 2;
  const totalSmsCount = 2; // 2 recipients * 1 SMS each
  const singleSmsMessage = mockT('sms:sending.teamMessage.warningSmsCountSingle')
    .replace('{total}', totalSmsCount.toString());
  
  logger.info(`Input: ${mockT('sms:sending.teamMessage.warningSmsCountSingle')}`);
  logger.info(`Output: ${singleSmsMessage}`);
  logger.info(`✅ Contains {total}: ${mockT('sms:sending.teamMessage.warningSmsCountSingle').includes('{total}')}`);
  logger.info(`✅ Replaced properly: ${!singleSmsMessage.includes('{total}')}\n`);

  // Test 3: Team Message Dialog - Multiple SMS
  logger.info('📋 Test 3: Team Message Dialog - Multiple SMS');
  const smsPerRecipient = 2;
  const totalMultipleSmsCount = recipientCount * smsPerRecipient; // 2 recipients * 2 SMS each = 4
  const multipleSmsMessage = mockT('sms:sending.teamMessage.warningSmsCountPlural')
    .replace('{smsPerRecipient}', smsPerRecipient.toString())
    .replace('{total}', totalMultipleSmsCount.toString());
  
  logger.info(`Input: ${mockT('sms:sending.teamMessage.warningSmsCountPlural')}`);
  logger.info(`Output: ${multipleSmsMessage}`);
  logger.info(`✅ Contains {smsPerRecipient}: ${mockT('sms:sending.teamMessage.warningSmsCountPlural').includes('{smsPerRecipient}')}`);
  logger.info(`✅ Contains {total}: ${mockT('sms:sending.teamMessage.warningSmsCountPlural').includes('{total}')}`);
  logger.info(`✅ Replaced properly: ${!multipleSmsMessage.includes('{smsPerRecipient}') && !multipleSmsMessage.includes('{total}')}\n`);

  // Test 4: Edge Cases
  logger.info('📋 Test 4: Edge Cases');
  
  // Test with 0 recipients
  const zeroRecipientsMessage = mockT('sms:sending.paymentReminder.warningPlural')
    .replace(/{count}/g, '0');
  logger.info(`Zero recipients: ${zeroRecipientsMessage}`);
  
  // Test with undefined/null values (should be handled by safe values)
  const safeRecipientCount = Math.max(0, 0 || 0);
  const safeSmsCount = Math.max(1, 0 || 1);
  const safeTotalSmsCount = safeRecipientCount * safeSmsCount;
  
  logger.info(`Safe recipient count: ${safeRecipientCount}`);
  logger.info(`Safe SMS count: ${safeSmsCount}`);
  logger.info(`Safe total SMS count: ${safeTotalSmsCount}`);
  
  const safeMessage = mockT('sms:sending.teamMessage.warningSmsCountSingle')
    .replace('{total}', safeTotalSmsCount.toString());
  logger.info(`Safe message: ${safeMessage}\n`);

  // Test 5: Simulate the exact scenario mentioned by user
  logger.info('📋 Test 5: User Scenario - 2 recipients');
  const userScenarioCount = 2;
  const userScenarioMessage = mockT('sms:sending.paymentReminder.warningPlural')
    .replace(/{count}/g, userScenarioCount.toString());
  
  logger.info(`User scenario input: ${mockT('sms:sending.paymentReminder.warningPlural')}`);
  logger.info(`User scenario output: ${userScenarioMessage}`);
  logger.info(`✅ Should show: "This will send Sms messages to 2 recipients and will consume 2 Sms credits from your balance."`);
  logger.info(`✅ Actually shows: "${userScenarioMessage}"`);
  logger.info(`✅ Match: ${userScenarioMessage === 'This will send Sms messages to 2 recipients and will consume 2 Sms credits from your balance.'}\n`);

  logger.info('✅ All translation tests completed!');
  logger.info('\n💡 If you\'re still seeing {count} not replaced in the UI:');
  logger.info('   1. Check browser console for any JavaScript errors');
  logger.info('   2. Verify the translation files are loaded correctly');
  logger.info('   3. Check if there are any race conditions in component rendering');
  logger.info('   4. Ensure the component state is updating properly');
}

// Run the test
testTranslationReplacement();
