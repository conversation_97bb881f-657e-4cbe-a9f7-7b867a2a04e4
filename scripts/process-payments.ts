#!/usr/bin/env tsx

import logger from '@/lib/logger';

/**
 * Manual Payment Processor CLI Script
 * 
 * This script allows manual execution of the payment processing logic
 * that is normally handled by the scheduled task.
 * 
 * Usage:
 *   tsx scripts/process-payments.ts                     # Process payments for today
 *   tsx scripts/process-payments.ts 2025-06-20         # Process payments for specific date
 *   tsx scripts/process-payments.ts --help             # Show help
 *   npm run process-payments                            # Using npm script
 *   npm run process-payments 2025-06-20               # Using npm script with date
 */

function showHelp() {
  logger.info(`
🏆 Payment Processor CLI - Sports Club Management System

📝 DESCRIPTION:
   Manually process payment assignments for athletes based on their active payment plans.
   This script performs the same operation as the scheduled task but can be run on-demand.

🚀 USAGE:
   tsx scripts/process-payments.ts [DATE] [OPTIONS]
   npm run process-payments [DATE]

📅 PARAMETERS:
   DATE       Optional. Process payments for specific date in YYYY-MM-DD format
              If not provided, processes payments for today.

🛠️  OPTIONS:
   --help     Show this help message

📋 EXAMPLES:
   tsx scripts/process-payments.ts                     # Process today's payments
   tsx scripts/process-payments.ts 2025-06-20         # Process payments for June 20, 2025
   npm run process-payments                            # Using npm script (today)
   npm run process-payments 2025-06-20               # Using npm script (specific date)

⚡ WHAT IT DOES:
   • Finds active payment plan assignments
   • Creates pending payments for athletes whose payment is due
   • Handles both new assignments and recurring monthly payments
   • Only processes active athletes with active payment plans
   • Prevents duplicate payments for the same assignment
   • Smart duplicate detection: Skips if payment already exists for current month
   • Handles plan assignment date changes after payment creation

💡 WHEN TO USE:
   • Scheduled task failed after all retries
   • System was down during scheduled payment processing
   • Need to process payments for a specific past date
   • Testing payment processing logic
   • Manual recovery after system issues

📊 OUTPUT:
   • Total assignments processed
   • Number of payments created
   • Any errors encountered during processing
   • Detailed execution log

🔧 ENVIRONMENT VARIABLES (Optional):
   • DATABASE_URL     - Database connection string
   • All other environment variables used by the main application

For more information, see: lib/scheduler/README.md
`);
}

async function main() {
  const args = process.argv.slice(2);
  
  // Handle help flag first, before loading any modules
  if (args.includes('--help') || args.includes('-h')) {
    showHelp();
    process.exit(0);
  }

  logger.info('🚀 Starting manual payment processing...');
  logger.info(`📅 Target date: ${args[0] || 'today'}`);
  logger.info(`🕒 Started at: ${new Date().toISOString()}`);
  logger.info('─'.repeat(50));

  try {
    const targetDate = args[0];

    // Validate date format early, before loading modules
    if (targetDate) {
      const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
      if (!dateRegex.test(targetDate)) {
        throw new Error(`
❌ Invalid date format: "${targetDate}"
   Expected format: YYYY-MM-DD (e.g., 2025-06-20)
   
   Examples:
   • tsx scripts/process-payments.ts 2025-06-20
   • npm run process-payments 2025-12-25
   
   For help: tsx scripts/process-payments.ts --help`);
      }
      
      // Validate that it's a valid date
      const parsedDate = new Date(targetDate);
      if (isNaN(parsedDate.getTime())) {
        throw new Error(`Invalid date: "${targetDate}". Please provide a valid date in YYYY-MM-DD format.`);
      }
    }

    // Only load the payment processor after validation (dynamic import for TypeScript)
    const { createPendingPaymentsForToday, createPendingPaymentsForDate } = await import('../lib/scheduler/payment-processor');

    let result;
    
    if (targetDate) {
      logger.info(`💰 Processing payments for specific date: ${targetDate}`);
      result = await createPendingPaymentsForDate(targetDate);
    } else {
      logger.info('💰 Processing payments for today');
      result = await createPendingPaymentsForToday();
    }

    logger.info('─'.repeat(50));
    logger.info('✅ Payment processing completed successfully!');
    logger.info(`📊 Results:`);
    logger.info(`   • Total assignments processed: ${result.totalAssignments}`);
    logger.info(`   • Payments created: ${result.paymentsCreated}`);
    logger.info(`   • Errors encountered: ${result.errors?.length || 0}`);
    
    if (result.errors && result.errors.length > 0) {
      logger.info('\n❌ Errors:');
      result.errors.forEach((error: string, index: number) => {
        logger.info(`   ${index + 1}. ${error}`);
      });
    }
    
    if (result.paymentsCreated === 0 && result.totalAssignments === 0) {
      logger.info('\n💡 No payment assignments found for processing.');
      logger.info('   This might be normal if:');
      logger.info('   • No payment plans are due today');
      logger.info('   • All eligible payments have already been created');
      logger.info('   • No active athletes with active payment plans');
    }
    
    logger.info(`🕒 Completed at: ${new Date().toISOString()}`);
    logger.info('─'.repeat(50));
    
    // Exit with success
    process.exit(0);
    
  } catch (error: unknown) {
    logger.info('─'.repeat(50));
    logger.error('❌ Payment processing failed:');
    
    // Special handling for missing payment processor module
    if (error instanceof Error && 'code' in error && error.code === 'MODULE_NOT_FOUND' && error.message.includes('payment-processor')) {
      logger.error(`   Error: Payment processor module not found`);
      logger.error(`   This indicates the payment scheduler system is not yet fully set up.`);
      logger.error(`   Please ensure the payment scheduler modules are properly installed.`);
      logger.info('\n💡 This feature requires the payment scheduler system to be implemented.');
      logger.info('   If you are a developer, check lib/scheduler/payment-processor.ts');
    } else if (error instanceof Error) {
      logger.error(`   Error: ${error.message}`);
      
      if (process.env.NODE_ENV === 'development') {
        logger.error(`   Stack: ${error.stack}`);
      }
      
      // Show help hint for common errors
      if (error.message.includes('Invalid date format') || error.message.includes('Invalid date')) {
        logger.info('\n💡 For help with date formats: tsx scripts/process-payments.ts --help');
      }
    } else {
      logger.error(`   Error: ${String(error)}`);
    }
    
    logger.info(`🕒 Failed at: ${new Date().toISOString()}`);
    logger.info('─'.repeat(50));
    
    // Exit with error
    process.exit(1);
  }
}

// Handle process termination gracefully
process.on('SIGINT', () => {
  logger.info('\n📥 Received SIGINT, stopping manual payment processing...');
  process.exit(130);
});

process.on('SIGTERM', () => {
  logger.info('\n📥 Received SIGTERM, stopping manual payment processing...');
  process.exit(143);
});

// Catch unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  logger.error('❌ Unhandled Rejection at:', { promise, reason });
  process.exit(1);
});

// Run the script
if (require.main === module) {
  main();
}
