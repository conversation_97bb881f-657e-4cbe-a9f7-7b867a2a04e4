/**
 * <PERSON><PERSON>t to seed SMS templates for testing
 * Run with: npx tsx scripts/seed-sms-templates.ts
 */

import { db } from '@/src/db';
import * as schema from '@/src/db/schema';
import { eq, and } from 'drizzle-orm';

const templates = [
  // English templates
  {
    type: 'general' as const,
    language: 'en',
    title: 'General Announcement',
    template: 'Dear {{parentName}}, we have an important announcement regarding {{schoolName}}. Please contact us for more information.',
    isActive: true
  },
  {
    type: 'event' as const,
    language: 'en',
    title: 'Event Notification',
    template: 'Dear {{parentName}}, {{schoolName}} has an upcoming event for {{athleteName}}. Please check your schedule and confirm attendance.',
    isActive: true
  },
  {
    type: 'meeting' as const,
    language: 'en',
    title: 'Meeting Call',
    template: 'Dear {{parentName}}, there will be a meeting regarding {{athleteName}} at {{schoolName}}. Your attendance is requested.',
    isActive: true
  },
  {
    type: 'holiday' as const,
    language: 'en',
    title: 'Holiday Notification',
    template: 'Dear {{parentName}}, {{schoolName}} will be closed for holidays. We wish you and {{athleteName}} a wonderful time.',
    isActive: true
  },

  // Turkish templates
  {
    type: 'general' as const,
    language: 'tr',
    title: 'Genel Duyuru',
    template: 'Sayın {{parentName}}, {{schoolName}} ile ilgili önemli bir duyurumuz bulunmaktadır. Daha fazla bilgi için lütfen bizimle iletişime geçin.',
    isActive: true
  },
  {
    type: 'event' as const,
    language: 'tr',
    title: 'Etkinlik Bildirimi',
    template: 'Sayın {{parentName}}, {{schoolName}}\'da {{athleteName}} için yaklaşan bir etkinliğimiz bulunmaktadır. Lütfen programınızı kontrol edin ve katılımınızı onaylayın.',
    isActive: true
  },
  {
    type: 'meeting' as const,
    language: 'tr',
    title: 'Toplantı Çağrısı',
    template: 'Sayın {{parentName}}, {{schoolName}}\'da {{athleteName}} ile ilgili bir toplantı yapılacaktır. Katılımınız rica olunur.',
    isActive: true
  },
  {
    type: 'holiday' as const,
    language: 'tr',
    title: 'Tatil Bildirimi',
    template: 'Sayın {{parentName}}, {{schoolName}} tatil nedeniyle kapalı olacaktır. Size ve {{athleteName}}\'e güzel zaman geçirmenizi dileriz.',
    isActive: true
  }
];

async function seedSmsTemplates() {
  console.log('🌱 Starting SMS templates seeding (global templates)...');

  try {
    for (const template of templates) {
      // Check if template already exists
      const existing = await db.select()
        .from(schema.smsTemplates)
        .where(and(
          eq(schema.smsTemplates.type, template.type),
          eq(schema.smsTemplates.language, template.language)
        ))
        .limit(1);

      if (existing.length > 0) {
        console.log(`⏭️  Template ${template.type} (${template.language}) already exists, skipping...`);
        continue;
      }

      // Insert new template
      await db.insert(schema.smsTemplates).values({
        type: template.type,
        language: template.language,
        title: template.title,
        template: template.template,
        isActive: template.isActive,
        createdBy: BigInt(1),
        updatedBy: BigInt(1),
      });

      console.log(`✅ Created template: ${template.type} (${template.language})`);
    }

    console.log('🎉 SMS templates seeding completed!');

    // Verify the seeded data
    const seededTemplates = await db.select()
      .from(schema.smsTemplates);

    console.log(`📊 Total templates in database: ${seededTemplates.length}`);
    console.log('📋 Templates by type and language:');

    const grouped = seededTemplates.reduce((acc, t) => {
      const key = `${t.type} (${t.language})`;
      acc[key] = t.isActive ? 'Active' : 'Inactive';
      return acc;
    }, {} as Record<string, string>);

    console.table(grouped);

  } catch (error) {
    console.error('❌ Error seeding SMS templates:', error);
  }
}

// Run the seeding
seedSmsTemplates().then(() => {
  console.log('✅ Seeding script completed');
  process.exit(0);
}).catch((error) => {
  console.error('❌ Seeding script failed:', error);
  process.exit(1);
});
