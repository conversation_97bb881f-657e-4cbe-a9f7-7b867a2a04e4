#!/usr/bin/env tsx

/**
 * Test Overdue Payments Update Script
 * 
 * This script allows manual testing of the overdue payment update logic
 * to debug why payments are not being marked as overdue.
 * 
 * Usage:
 *   tsx scripts/test-overdue-payments.ts
 *   npm run test-overdue-payments
 */

import { runOverdueUpdateForTenant } from '../lib/payment-scheduler';
import { db } from '../src/db';
import { payments } from '../src/db/schema';
import { eq, and, lte } from 'drizzle-orm';
import logger from '@/lib/logger';

async function main() {
  try {
    logger.info('🔍 Testing Overdue Payments Update');
    logger.info('=====================================');
    
    const today = new Date();
    const todayString = today.toISOString().split('T')[0];
    logger.info(`📅 Today: ${todayString}`);
    
    // First, let's check what pending payments exist with due dates <= today
    logger.info('\n📋 Checking pending payments with due dates <= today...');
    
    const pendingPayments = await db
      .select({
        id: payments.id,
        tenantId: payments.tenantId,
        athleteId: payments.athleteId,
        amount: payments.amount,
        dueDate: payments.dueDate,
        status: payments.status,
        description: payments.description
      })
      .from(payments)
      .where(
        and(
          eq(payments.status, "pending"),
          lte(payments.dueDate, todayString)
        )
      );
    
    logger.info(`Found ${pendingPayments.length} pending payments with due dates <= ${todayString}:`);
    
    if (pendingPayments.length === 0) {
      logger.info('ℹ️  No pending payments found that should be overdue.');
      logger.info('   This could mean:');
      logger.info('   1. All payments are already marked as overdue');
      logger.info('   2. No payments have due dates <= today');
      logger.info('   3. All payments are in other statuses (paid, cancelled, etc.)');
    } else {
      // Group by tenant
      const paymentsByTenant = pendingPayments.reduce((acc, payment) => {
        if (!acc[payment.tenantId]) {
          acc[payment.tenantId] = [];
        }
        acc[payment.tenantId].push(payment);
        return acc;
      }, {} as Record<string, typeof pendingPayments>);
      
      logger.info('\n📊 Payments by tenant:');
      Object.entries(paymentsByTenant).forEach(([tenantId, tenantPayments]) => {
        logger.info(`\n🏢 Tenant: ${tenantId}`);
        tenantPayments.forEach(payment => {
          logger.info(`   - ID: ${payment.id.substring(0, 8)}... | Due: ${payment.dueDate} | Amount: ${payment.amount} | ${payment.description}`);
        });
      });
      
      // Now run the overdue update for each tenant
      logger.info('\n🔄 Running overdue payment updates...');
      
      for (const tenantId of Object.keys(paymentsByTenant)) {
        logger.info(`\n🏢 Processing tenant: ${tenantId}`);
        try {
          const result = await runOverdueUpdateForTenant(tenantId);
          logger.info(`✅ Updated ${result.length} payments to overdue status for tenant ${tenantId}`);
          
          if (result.length > 0) {
            result.forEach(payment => {
              logger.info(`   - Updated: ${payment.id.substring(0, 8)}... | Due: ${payment.dueDate} | Amount: ${payment.amount}`);
            });
          }
        } catch (error) {
          logger.error(`❌ Error processing tenant ${tenantId}:`, { error });
        }
      }
    }
    
    // Final check - show all overdue payments
    logger.info('\n📋 Final check - all overdue payments:');
    const overduePayments = await db
      .select({
        id: payments.id,
        tenantId: payments.tenantId,
        dueDate: payments.dueDate,
        amount: payments.amount,
        status: payments.status,
        updatedAt: payments.updatedAt
      })
      .from(payments)
      .where(eq(payments.status, "overdue"));
    
    logger.info(`Found ${overduePayments.length} overdue payments total:`);
    overduePayments.forEach(payment => {
      logger.info(`   - ID: ${payment.id.substring(0, 8)}... | Due: ${payment.dueDate} | Amount: ${payment.amount} | Updated: ${payment.updatedAt?.toISOString()}`);
    });
    
    logger.info('\n✅ Test completed successfully!');
    
  } catch (error) {
    logger.error('❌ Test failed:', { error });
    process.exit(1);
  }
}

// Run the script
main().catch(error => logger.error('Unhandled error in main:', { error }));
