/**
 * <PERSON><PERSON><PERSON> to initialize SMS balance for testing
 * Run with: npx tsx scripts/init-sms-balance.ts
 */

import { db } from '@/src/db';
import * as schema from '@/src/db/schema';
import { eq } from 'drizzle-orm';

async function initializeSmsBalance() {
  console.log('🔄 Initializing SMS balance...');

  try {
    // Get a sample tenant ID from existing data
    const existingTenant = await db.select({ tenantId: schema.athletes.tenantId })
      .from(schema.athletes)
      .limit(1);

    if (existingTenant.length === 0) {
      console.error('❌ No existing tenant found. Please create some data first.');
      return;
    }

    const tenantId = existingTenant[0].tenantId;
    console.log(`📋 Using tenant ID: ${tenantId}`);

    // Check if SMS balance already exists
    const existingBalance = await db.select()
      .from(schema.smsBalance)
      .where(eq(schema.smsBalance.tenantId, tenantId))
      .limit(1);

    if (existingBalance.length > 0) {
      console.log(`✅ SMS balance already exists: ${existingBalance[0].balance} credits`);
      console.log('Current balance details:', existingBalance[0]);
      return;
    }

    // Get a sample user ID for created/updated by
    const existingUser = await db.select({ createdBy: schema.athletes.createdBy })
      .from(schema.athletes)
      .where(eq(schema.athletes.tenantId, tenantId))
      .limit(1);

    const userId = existingUser[0]?.createdBy || BigInt(1);

    // Create initial SMS balance with 1000 credits for testing
    const newBalance = await db.insert(schema.smsBalance).values({
      tenantId,
      balance: 1000, // Start with 1000 credits for testing
      createdBy: userId,
      updatedBy: userId,
    }).returning();

    console.log('✅ Created SMS balance:', newBalance[0]);
    console.log(`🎉 SMS balance initialized with ${newBalance[0].balance} credits!`);

  } catch (error) {
    console.error('❌ Error initializing SMS balance:', error);
  }
}

// Run the initialization
initializeSmsBalance().then(() => {
  console.log('✅ SMS balance initialization completed');
  process.exit(0);
}).catch((error) => {
  console.error('❌ SMS balance initialization failed:', error);
  process.exit(1);
});
